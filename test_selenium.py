#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Selenium自动登录脚本
"""

import sys
import traceback

def test_imports():
    """测试所有必要的模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        import selenium
        print(f"✅ Selenium版本: {selenium.__version__}")
    except ImportError as e:
        print(f"❌ Selenium导入失败: {e}")
        return False
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        print("✅ Selenium WebDriver导入成功")
    except ImportError as e:
        print(f"❌ Selenium WebDriver导入失败: {e}")
        return False
    
    try:
        import captcha_solver
        print("✅ captcha_solver导入成功")
    except ImportError as e:
        print(f"❌ captcha_solver导入失败: {e}")
        return False
    
    try:
        import paddleocr
        print("✅ PaddleOCR导入成功")
    except ImportError as e:
        print(f"❌ PaddleOCR导入失败: {e}")
        print("请运行: pip install paddleocr")
        return False
    
    return True

def test_chrome_driver():
    """测试Chrome驱动是否可用"""
    print("\n🧪 测试Chrome驱动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.get("https://www.baidu.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Chrome驱动测试成功，访问百度标题: {title}")
        return True
        
    except Exception as e:
        print(f"❌ Chrome驱动测试失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        print("ChromeDriver下载地址: https://chromedriver.chromium.org/")
        return False

def test_captcha_solver():
    """测试验证码解析器"""
    print("\n🧪 测试验证码解析器...")
    
    try:
        import captcha_solver
        
        # 检查是否有测试HTML文件
        import os
        if os.path.exists('1.html'):
            print("✅ 找到测试HTML文件: 1.html")
            
            # 测试提取验证码信息
            info = captcha_solver.extract_captcha_info('1.html')
            if info['success']:
                print(f"✅ 验证码信息提取成功")
                print(f"   需要点击的字符: {info['required_chars']}")
                print(f"   图像尺寸: {info['image_size']}")
            else:
                print(f"❌ 验证码信息提取失败: {info['error']}")
                return False
        else:
            print("⚠️  未找到测试HTML文件 (1.html)")
            print("   将跳过验证码解析器测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证码解析器测试失败: {e}")
        return False

def test_selenium_script():
    """测试主要的Selenium脚本"""
    print("\n🧪 测试Selenium脚本导入...")
    
    try:
        import selenium_auto_login
        print("✅ selenium_auto_login模块导入成功")
        
        # 测试类初始化（无头模式）
        print("测试AutoLoginBot类初始化...")
        bot = selenium_auto_login.AutoLoginBot(headless=True, timeout=10)
        print("✅ AutoLoginBot初始化成功")
        
        # 测试打开网站
        print("测试打开百度网站...")
        if bot.open_website("https://www.baidu.com"):
            print("✅ 网站打开测试成功")
        else:
            print("❌ 网站打开测试失败")
            return False
        
        # 关闭浏览器
        bot.close()
        print("✅ 浏览器关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Selenium脚本测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 Selenium自动登录脚本测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("Chrome驱动", test_chrome_driver),
        ("验证码解析器", test_captcha_solver),
        ("Selenium脚本", test_selenium_script)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！脚本可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查环境配置。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        traceback.print_exc()
        sys.exit(1)

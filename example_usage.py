#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA解决器使用示例
演示如何以编程方式使用captcha_solver模块
"""

import captcha_solver

def example_basic_usage():
    """基本使用示例"""
    print("🔥 基本使用示例")
    print("-" * 40)
    
    html_file = '1.html'
    
    # 方法1: 完整的解决方案
    print("1. 完整的CAPTCHA解决方案:")
    result = captcha_solver.solve_captcha(html_file, verbose=False)
    
    if result['success']:
        print(f"   ✅ 成功! 成功率: {result['statistics']['success_rate']:.1%}")
        print(f"   📝 字符序列: {result['required_chars']}")
        print(f"   📍 坐标: {result['coordinates']}")
        print(f"   ⏱️  处理时间: {result['processing_time']:.2f}秒")
    else:
        print(f"   ❌ 失败: {result['error']}")
    
    # 方法2: 仅获取坐标
    print("\n2. 仅获取坐标:")
    coordinates = captcha_solver.get_coordinates_only(html_file, verbose=False)
    print(f"   📍 坐标列表: {coordinates}")
    
    # 方法3: 仅提取CAPTCHA信息（不进行OCR）
    print("\n3. 仅提取CAPTCHA信息:")
    info = captcha_solver.extract_captcha_info(html_file)
    if info['success']:
        print(f"   📝 字符序列: {info['required_chars']}")
        print(f"   📏 图像尺寸: {info['image_size']}")
        print(f"   📄 指令文本: '{info['instruction_text']}'")
    else:
        print(f"   ❌ 失败: {info['error']}")


def example_advanced_usage():
    """高级使用示例"""
    print("\n🚀 高级使用示例")
    print("-" * 40)
    
    html_file = '1.html'
    
    # 自定义参数
    print("1. 使用自定义参数:")
    result = captcha_solver.solve_captcha(
        html_file,
        use_gpu=False,                    # 不使用GPU
        similarity_threshold=0.5,         # 降低相似度阈值
        save_debug_images=True,           # 保存调试图像
        verbose=False                     # 静默模式
    )
    
    if result['success']:
        print(f"   ✅ 自定义参数解决成功")
        print(f"   📊 详细统计: {result['statistics']}")
        
        # 分析每个点击步骤
        print("   📍 详细点击步骤:")
        for step in result['click_sequence']:
            if step.get('coordinate'):
                print(f"      第{step['step']}步: '{step['character']}' → "
                      f"坐标{step['coordinate']} (匹配: '{step['matched_char']}', "
                      f"相似度: {step['similarity']:.3f})")
            else:
                print(f"      第{step['step']}步: '{step['character']}' → "
                      f"❌ {step.get('error', '未知错误')}")
    else:
        print(f"   ❌ 失败: {result['error']}")


def example_batch_processing():
    """批量处理示例"""
    print("\n📦 批量处理示例")
    print("-" * 40)
    
    # 假设有多个HTML文件
    html_files = ['1.html']  # 实际使用时可以添加更多文件
    
    if len(html_files) > 0:
        print(f"批量处理 {len(html_files)} 个文件:")
        results = captcha_solver.batch_solve_captcha(
            html_files, 
            verbose=False,
            save_debug_images=False
        )
        
        # 统计结果
        success_count = sum(1 for r in results.values() if r['success'])
        print(f"   📊 成功: {success_count}/{len(html_files)} 个文件")
        
        for filename, result in results.items():
            if result['success']:
                print(f"   ✅ {filename}: {result['statistics']['success_rate']:.1%} 成功率")
            else:
                print(f"   ❌ {filename}: {result['error']}")
    else:
        print("   没有文件可供批量处理")


def example_error_handling():
    """错误处理示例"""
    print("\n🛡️  错误处理示例")
    print("-" * 40)
    
    # 测试不存在的文件
    print("1. 处理不存在的文件:")
    result = captcha_solver.solve_captcha('nonexistent.html')
    print(f"   结果: {'成功' if result['success'] else '失败'}")
    if not result['success']:
        print(f"   错误: {result['error']}")
    
    # 测试空文件路径
    print("\n2. 处理空文件路径:")
    try:
        result = captcha_solver.solve_captcha('')
        print(f"   结果: {'成功' if result['success'] else '失败'}")
        if not result['success']:
            print(f"   错误: {result['error']}")
    except Exception as e:
        print(f"   异常: {e}")


def example_integration():
    """集成使用示例"""
    print("\n🔗 集成使用示例")
    print("-" * 40)
    
    html_file = '1.html'
    
    # 模拟自动化流程
    print("模拟自动化点击流程:")
    
    # 步骤1: 获取坐标
    coordinates = captcha_solver.get_coordinates_only(html_file, verbose=False)
    
    if coordinates:
        print("   📍 获取到坐标，模拟点击:")
        for i, coord in enumerate(coordinates):
            if coord:
                x, y = coord
                print(f"      第{i+1}步: 点击坐标 ({x}, {y})")
                # 这里可以集成实际的点击操作
                # import pyautogui
                # pyautogui.click(x, y)
                # time.sleep(0.5)
            else:
                print(f"      第{i+1}步: 坐标缺失，跳过")
    else:
        print("   ❌ 未获取到有效坐标")


if __name__ == "__main__":
    print("🎯 CAPTCHA解决器模块使用示例")
    print("=" * 60)
    
    try:
        example_basic_usage()
        example_advanced_usage()
 
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成!")
        print("\n💡 提示:")
        print("   - 可以根据需要调整参数")
        print("   - 支持GPU加速（需要CUDA环境）")
        print("   - 可以保存调试图像进行分析")
        print("   - 支持批量处理多个文件")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()

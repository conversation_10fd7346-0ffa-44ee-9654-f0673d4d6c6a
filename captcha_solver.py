#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA验证码自动解决器 - 生产版本
整合HTML解析、OCR识别和坐标匹配功能的完整解决方案

这是一个完整的CAPTCHA验证码自动解决器，包含以下功能：
1. HTML解析：从HTML文件中提取CAPTCHA图像和指令文本
2. 增强OCR识别：使用PaddleOCR进行多方法字符识别
3. 强健坐标匹配：智能匹配字符并处理OCR错误
4. 准确率评估：提供详细的匹配统计和质量分析

命令行使用方法：
    python captcha_solver.py

模块导入使用方法：
    import captcha_solver

    # 方法1: 从文件路径
    result = captcha_solver.solve_captcha('1.html')
    if result['success']:
        coordinates = result['coordinates']
        print(f"坐标: {coordinates}")

    # 方法2: 从HTML文本内容
    html_content = '<div>请依次点击【日,无,逊,莽】</div><img src="data:image/png;base64,..." />'
    result = captcha_solver.solve_captcha_from_html(html_content)
    if result['success']:
        coordinates = result['coordinates']
        print(f"坐标: {coordinates}")

    # 仅获取坐标
    coordinates = captcha_solver.get_coordinates_only('1.html')
    coordinates = captcha_solver.get_coordinates_from_html(html_content)

    # 提取CAPTCHA信息
    info = captcha_solver.extract_captcha_info('1.html')
    info = captcha_solver.extract_captcha_info_from_html(html_content)
    print(f"需要点击的字符: {info['required_chars']}")

    # 批量处理
    results = captcha_solver.batch_solve_captcha(['file1.html', 'file2.html'])

依赖库：
    pip install paddleocr pillow opencv-python beautifulsoup4 numpy lxml
"""

import os
import sys
import time
import re
import base64
import io
import numpy as np
import cv2
import difflib
from typing import List, Tuple, Optional, Dict
from collections import defaultdict
from PIL import Image, ImageEnhance, ImageFilter
from bs4 import BeautifulSoup


# ============================================================================
# HTML解析器类
# ============================================================================

class CaptchaHTMLParser:
    """CAPTCHA HTML解析器"""

    def __init__(self, html_source: str, is_file_path: bool = True):
        """
        初始化解析器

        Args:
            html_source: HTML文件路径或HTML文本内容
            is_file_path: 是否为文件路径 (True) 或HTML文本 (False)
        """
        self.html_source = html_source
        self.is_file_path = is_file_path
        self.soup = None
        self.captcha_image = None
        self.instruction_text = ""
        self.required_chars = []

    def parse_html(self) -> bool:
        """
        解析HTML文件或HTML文本

        Returns:
            bool: 解析是否成功
        """
        try:
            if self.is_file_path:
                # 从文件读取HTML内容
                with open(self.html_source, 'r', encoding='utf-8') as f:
                    html_content = f.read()
            else:
                # 直接使用HTML文本内容
                html_content = self.html_source

            self.soup = BeautifulSoup(html_content, 'html.parser')
            return True
        except Exception as e:
            source_type = "HTML文件" if self.is_file_path else "HTML文本"
            print(f"解析{source_type}失败: {e}")
            return False

    def extract_captcha_image(self) -> Optional[Image.Image]:
        """
        提取CAPTCHA图像

        Returns:
            PIL.Image: CAPTCHA图像对象，如果提取失败返回None
        """
        try:
            # 查找包含base64图像数据的img标签
            img_tags = self.soup.find_all('img')

            for img in img_tags:
                src = img.get('src', '')
                if src.startswith('data:image/'):
                    # 提取base64数据
                    base64_data = src.split(',')[1]
                    image_data = base64.b64decode(base64_data)

                    # 转换为PIL图像
                    self.captcha_image = Image.open(io.BytesIO(image_data))
                    return self.captcha_image

            print("未找到CAPTCHA图像")
            return None

        except Exception as e:
            print(f"提取CAPTCHA图像失败: {e}")
            return None

    def extract_instruction_text(self) -> str:
        """
        提取指令文本

        Returns:
            str: 指令文本
        """
        try:
            # 查找包含指令的文本
            # 通常指令文本包含"请依次点击"或类似的文字
            text_content = self.soup.get_text()

            # 使用正则表达式匹配指令文本
            patterns = [
                r'请依次点击【([^】]+)】',
                r'请按顺序点击[：:]\s*([^，,\s]+(?:[，,]\s*[^，,\s]+)*)',
                r'请点击[：:]?\s*([^，,\s]+(?:[，,]\s*[^，,\s]+)*)',
                r'按顺序点击[：:]?\s*([^，,\s]+(?:[，,]\s*[^，,\s]+)*)'
            ]

            for pattern in patterns:
                match = re.search(pattern, text_content)
                if match:
                    self.instruction_text = match.group(1)
                    break

            if not self.instruction_text:
                # 如果没有找到标准格式，尝试查找包含中文字符的序列
                chinese_char_pattern = r'([一-龯]+(?:[，,]\s*[一-龯]+)*)'
                matches = re.findall(chinese_char_pattern, text_content)
                if matches:
                    # 选择最可能的指令文本（通常是较短的字符序列）
                    self.instruction_text = min(matches, key=len)

            return self.instruction_text

        except Exception as e:
            print(f"提取指令文本失败: {e}")
            return ""

    def parse_required_characters(self) -> List[str]:
        """
        解析需要点击的字符序列

        Returns:
            List[str]: 需要点击的字符列表
        """
        if not self.instruction_text:
            return []

        try:
            # 分割字符串，支持逗号、中文逗号等分隔符
            chars = re.split(r'[，,、\s]+', self.instruction_text.strip())
            # 过滤空字符串并确保每个元素都是单个字符
            self.required_chars = [char.strip() for char in chars if char.strip()]

            return self.required_chars

        except Exception as e:
            print(f"解析字符序列失败: {e}")
            return []

    def get_captcha_info(self) -> Tuple[Optional[Image.Image], List[str]]:
        """
        获取完整的CAPTCHA信息

        Returns:
            Tuple[PIL.Image, List[str]]: (CAPTCHA图像, 需要点击的字符列表)
        """
        if not self.parse_html():
            return None, []

        image = self.extract_captcha_image()
        self.extract_instruction_text()
        chars = self.parse_required_characters()

        return image, chars

    def save_captcha_image(self, output_path: str) -> bool:
        """
        保存CAPTCHA图像到文件

        Args:
            output_path: 输出文件路径

        Returns:
            bool: 保存是否成功
        """
        try:
            if self.captcha_image:
                self.captcha_image.save(output_path)
                return True
            return False
        except Exception as e:
            print(f"保存图像失败: {e}")
            return False


# ============================================================================
# 增强版OCR检测器类
# ============================================================================

class EnhancedOCRDetector:
    """增强版OCR字符检测器"""

    def __init__(self, use_gpu: bool = False, lang: str = 'ch'):
        """
        初始化增强版OCR检测器

        Args:
            use_gpu: 是否使用GPU加速
            lang: 语言设置，'ch'表示中文
        """
        self.use_gpu = use_gpu
        self.lang = lang
        self.ocr = None
        self._init_ocr()

    def _init_ocr(self):
        """初始化PaddleOCR with optimized parameters"""
        try:
            from paddleocr import PaddleOCR
            # 使用默认参数初始化，后续动态调整
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang=self.lang,
                use_gpu=self.use_gpu,
                show_log=False
            )
            print("增强版PaddleOCR初始化成功 - 支持动态参数调整")
        except ImportError:
            print("错误: 请安装PaddleOCR库")
            print("安装命令: pip install paddleocr")
            raise
        except Exception as e:
            print(f"PaddleOCR初始化失败: {e}")
            raise

    def analyze_image_characteristics(self, image: Image.Image) -> Dict:
        """分析图像特征以确定最佳OCR参数"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为灰度
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

        characteristics = {}

        # 1. 图像尺寸
        height, width = gray.shape
        characteristics['width'] = width
        characteristics['height'] = height
        characteristics['size_category'] = self._categorize_size(width, height)

        # 2. 亮度分析
        brightness = np.mean(gray)
        characteristics['brightness'] = brightness / 255.0
        characteristics['brightness_category'] = self._categorize_brightness(brightness)

        # 3. 对比度分析
        contrast = np.std(gray)
        characteristics['contrast'] = contrast / 255.0
        characteristics['contrast_category'] = self._categorize_contrast(contrast)

        # 4. 噪声水平
        noise_level = self._estimate_noise_level(gray)
        characteristics['noise_level'] = noise_level
        characteristics['noise_category'] = self._categorize_noise(noise_level)

        # 5. 边缘密度
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        characteristics['edge_density'] = edge_density
        characteristics['complexity'] = self._categorize_complexity(edge_density)

        # 6. 文本区域估计
        text_regions = self._estimate_text_regions(gray)
        characteristics['estimated_char_count'] = text_regions

        return characteristics

    def _categorize_size(self, width: int, height: int) -> str:
        """分类图像尺寸"""
        if width < 150 or height < 50:
            return "small"
        elif width > 400 or height > 150:
            return "large"
        else:
            return "medium"

    def _categorize_brightness(self, brightness: float) -> str:
        """分类图像亮度"""
        if brightness < 85:
            return "dark"
        elif brightness > 170:
            return "bright"
        else:
            return "normal"

    def _categorize_contrast(self, contrast: float) -> str:
        """分类图像对比度"""
        if contrast < 30:
            return "low"
        elif contrast > 80:
            return "high"
        else:
            return "normal"

    def _categorize_noise(self, noise_level: float) -> str:
        """分类噪声水平"""
        if noise_level < 0.05:
            return "low"
        elif noise_level > 0.15:
            return "high"
        else:
            return "medium"

    def _categorize_complexity(self, edge_density: float) -> str:
        """分类图像复杂度"""
        if edge_density < 0.1:
            return "simple"
        elif edge_density > 0.3:
            return "complex"
        else:
            return "medium"

    def _estimate_text_regions(self, gray: np.ndarray) -> int:
        """估计文本区域数量"""
        # 使用连通组件分析估计字符数量
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤合理大小的轮廓
        valid_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 5000:  # 合理的字符区域大小
                valid_contours.append(contour)

        return len(valid_contours)

    def get_adaptive_ocr_params(self, characteristics: Dict) -> Dict:
        """根据图像特征获取自适应OCR参数"""
        params = {
            'drop_score': 0.2,
            'det_db_thresh': 0.2,
            'det_db_box_thresh': 0.4,
            'det_db_unclip_ratio': 2.0,
            'rec_batch_num': 8,
            'max_text_length': 30
        }

        # 根据图像尺寸调整
        if characteristics['size_category'] == 'small':
            params['drop_score'] = 0.1  # 更低的阈值
            params['det_db_thresh'] = 0.1
            params['det_db_box_thresh'] = 0.3
            params['det_db_unclip_ratio'] = 2.5
        elif characteristics['size_category'] == 'large':
            params['drop_score'] = 0.3
            params['det_db_thresh'] = 0.3
            params['det_db_box_thresh'] = 0.5

        # 根据对比度调整
        if characteristics['contrast_category'] == 'low':
            params['drop_score'] *= 0.7  # 降低阈值
            params['det_db_thresh'] *= 0.7
        elif characteristics['contrast_category'] == 'high':
            params['drop_score'] *= 1.3  # 提高阈值
            params['det_db_thresh'] *= 1.3

        # 根据噪声水平调整
        if characteristics['noise_category'] == 'high':
            params['drop_score'] *= 1.2
            params['det_db_thresh'] *= 1.2
        elif characteristics['noise_category'] == 'low':
            params['drop_score'] *= 0.8
            params['det_db_thresh'] *= 0.8

        # 根据复杂度调整
        if characteristics['complexity'] == 'complex':
            params['det_db_unclip_ratio'] = 2.5
            params['rec_batch_num'] = 6
        elif characteristics['complexity'] == 'simple':
            params['det_db_unclip_ratio'] = 1.8
            params['rec_batch_num'] = 10

        # 确保参数在合理范围内
        params['drop_score'] = max(0.05, min(0.5, params['drop_score']))
        params['det_db_thresh'] = max(0.05, min(0.5, params['det_db_thresh']))
        params['det_db_box_thresh'] = max(0.2, min(0.8, params['det_db_box_thresh']))
        params['det_db_unclip_ratio'] = max(1.5, min(3.0, params['det_db_unclip_ratio']))

        return params

    def preprocess_image_basic(self, image: Image.Image) -> np.ndarray:
        """基础图像预处理"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为BGR
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 基础增强
        alpha = 1.3  # 增加对比度
        beta = 15    # 增加亮度
        img_enhanced = cv2.convertScaleAbs(img_bgr, alpha=alpha, beta=beta)

        return img_enhanced

    def preprocess_image_advanced(self, image: Image.Image) -> np.ndarray:
        """高级图像预处理"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为BGR
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 1. 去噪处理
        img_denoised = cv2.fastNlMeansDenoisingColored(img_bgr, None, 10, 10, 7, 21)

        # 2. 转换为灰度图
        img_gray = cv2.cvtColor(img_denoised, cv2.COLOR_BGR2GRAY)

        # 3. 直方图均衡化
        img_equalized = cv2.equalizeHist(img_gray)

        # 4. 高斯模糊去噪
        img_blurred = cv2.GaussianBlur(img_equalized, (3, 3), 0)

        # 5. 锐化处理
        kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        img_sharpened = cv2.filter2D(img_blurred, -1, kernel_sharpen)

        # 6. 自适应阈值二值化
        img_binary = cv2.adaptiveThreshold(
            img_sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # 7. 形态学操作去除噪点
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        img_morphed = cv2.morphologyEx(img_binary, cv2.MORPH_CLOSE, kernel)

        # 转换回BGR格式
        img_result = cv2.cvtColor(img_morphed, cv2.COLOR_GRAY2BGR)

        return img_result

    def preprocess_image_otsu(self, image: Image.Image) -> np.ndarray:
        """使用Otsu阈值的预处理"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为灰度图
        img_gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

        # 高斯模糊
        img_blurred = cv2.GaussianBlur(img_gray, (5, 5), 0)

        # Otsu阈值二值化
        _, img_binary = cv2.threshold(img_blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        img_morphed = cv2.morphologyEx(img_binary, cv2.MORPH_CLOSE, kernel)

        # 转换回BGR格式
        img_result = cv2.cvtColor(img_morphed, cv2.COLOR_GRAY2BGR)

        return img_result

    def preprocess_image_pil_enhanced(self, image: Image.Image) -> np.ndarray:
        """使用PIL增强的预处理"""
        # PIL增强
        enhancer = ImageEnhance.Contrast(image)
        img_contrast = enhancer.enhance(1.5)

        enhancer = ImageEnhance.Brightness(img_contrast)
        img_bright = enhancer.enhance(1.2)

        enhancer = ImageEnhance.Sharpness(img_bright)
        img_sharp = enhancer.enhance(2.0)

        # 应用滤镜
        img_filtered = img_sharp.filter(ImageFilter.MedianFilter(size=3))

        # 转换为numpy数组
        img_array = np.array(img_filtered)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为BGR
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        return img_bgr

    def preprocess_image_captcha_optimized(self, image: Image.Image) -> np.ndarray:
        """专门针对验证码优化的预处理方法"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为BGR
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 1. 图像放大 - 提高小字符的识别率
        height, width = img_bgr.shape[:2]
        if width < 200 or height < 60:
            scale_factor = max(200/width, 60/height, 2.0)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img_bgr = cv2.resize(img_bgr, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # 2. 转换为灰度图
        img_gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

        # 3. 高斯模糊去噪
        img_blurred = cv2.GaussianBlur(img_gray, (3, 3), 0)

        # 4. 多种二值化方法组合
        # 方法1: Otsu阈值
        _, img_otsu = cv2.threshold(img_blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 方法2: 自适应阈值
        img_adaptive = cv2.adaptiveThreshold(
            img_blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # 方法3: 固定阈值
        _, img_fixed = cv2.threshold(img_blurred, 127, 255, cv2.THRESH_BINARY)

        # 选择最佳二值化结果（基于前景像素比例）
        otsu_ratio = np.sum(img_otsu == 0) / img_otsu.size
        adaptive_ratio = np.sum(img_adaptive == 0) / img_adaptive.size
        fixed_ratio = np.sum(img_fixed == 0) / img_fixed.size

        # 选择前景比例在合理范围内的结果
        ratios = [(otsu_ratio, img_otsu), (adaptive_ratio, img_adaptive), (fixed_ratio, img_fixed)]
        best_img = min(ratios, key=lambda x: abs(x[0] - 0.3))[1]  # 假设理想前景比例为30%

        # 5. 形态学操作去除噪点
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        img_morphed = cv2.morphologyEx(best_img, cv2.MORPH_CLOSE, kernel)

        # 6. 边缘增强
        kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        img_sharpened = cv2.filter2D(img_morphed, -1, kernel_sharpen)

        # 转换回BGR格式
        img_result = cv2.cvtColor(img_sharpened, cv2.COLOR_GRAY2BGR)

        return img_result

    def preprocess_image_multi_scale(self, image: Image.Image) -> np.ndarray:
        """多尺度处理方法"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为BGR
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 1. 多尺度放大处理
        height, width = img_bgr.shape[:2]

        # 尝试不同的放大倍数
        scales = [1.5, 2.0, 2.5, 3.0]
        best_result = None
        best_score = 0

        for scale in scales:
            new_width = int(width * scale)
            new_height = int(height * scale)

            # 使用不同的插值方法
            for interpolation in [cv2.INTER_CUBIC, cv2.INTER_LANCZOS4]:
                scaled = cv2.resize(img_bgr, (new_width, new_height), interpolation=interpolation)

                # 转换为灰度
                gray = cv2.cvtColor(scaled, cv2.COLOR_BGR2GRAY)

                # 计算图像质量分数（基于边缘强度）
                edges = cv2.Canny(gray, 50, 150)
                edge_density = np.sum(edges > 0) / edges.size

                # 计算对比度
                contrast = gray.std()

                # 综合分数
                score = edge_density * contrast

                if score > best_score:
                    best_score = score
                    best_result = scaled

        if best_result is None:
            best_result = img_bgr

        # 2. 高级去噪和增强
        gray = cv2.cvtColor(best_result, cv2.COLOR_BGR2GRAY)

        # 双边滤波去噪
        denoised = cv2.bilateralFilter(gray, 9, 75, 75)

        # CLAHE (对比度限制自适应直方图均衡化)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # 锐化
        kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel_sharpen)

        # 转换回BGR
        result = cv2.cvtColor(sharpened, cv2.COLOR_GRAY2BGR)

        return result

    def preprocess_image_intelligent_denoise(self, image: Image.Image) -> np.ndarray:
        """智能去噪处理"""
        img_array = np.array(image)

        # 处理RGBA
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)

        # 转换为BGR
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 1. 分析图像噪声类型
        gray = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2GRAY)

        # 计算噪声水平
        noise_level = self._estimate_noise_level(gray)

        # 2. 根据噪声水平选择去噪策略
        if noise_level > 0.1:  # 高噪声
            # 使用非局部均值去噪
            denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)
        elif noise_level > 0.05:  # 中等噪声
            # 使用双边滤波
            denoised = cv2.bilateralFilter(gray, 9, 75, 75)
        else:  # 低噪声
            # 使用高斯滤波
            denoised = cv2.GaussianBlur(gray, (3, 3), 0)

        # 3. 自适应增强
        # 计算局部对比度
        local_contrast = self._calculate_local_contrast(denoised)

        if local_contrast < 0.3:  # 低对比度
            # 使用CLAHE增强
            clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
        else:
            enhanced = denoised

        # 4. 智能二值化
        # 使用多种阈值方法并选择最佳
        _, otsu = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        adaptive = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

        # 选择更好的二值化结果
        otsu_score = self._evaluate_binarization(enhanced, otsu)
        adaptive_score = self._evaluate_binarization(enhanced, adaptive)

        if otsu_score > adaptive_score:
            binary = otsu
        else:
            binary = adaptive

        # 5. 形态学后处理
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        # 转换回BGR
        result = cv2.cvtColor(cleaned, cv2.COLOR_GRAY2BGR)

        return result

    def _estimate_noise_level(self, image: np.ndarray) -> float:
        """估计图像噪声水平"""
        # 使用拉普拉斯算子估计噪声
        laplacian = cv2.Laplacian(image, cv2.CV_64F)
        noise_level = laplacian.var() / (image.mean() + 1e-6)
        return min(noise_level / 1000.0, 1.0)  # 归一化到0-1

    def _calculate_local_contrast(self, image: np.ndarray) -> float:
        """计算局部对比度"""
        # 使用标准差衡量对比度
        return image.std() / 255.0

    def _evaluate_binarization(self, original: np.ndarray, binary: np.ndarray) -> float:
        """评估二值化质量"""
        # 计算边缘保持度
        edges_orig = cv2.Canny(original, 50, 150)
        edges_bin = cv2.Canny(binary, 50, 150)

        # 计算边缘相似度
        intersection = np.logical_and(edges_orig, edges_bin)
        union = np.logical_or(edges_orig, edges_bin)

        if np.sum(union) == 0:
            return 0.0

        return np.sum(intersection) / np.sum(union)

    def detect_characters_multi_method(self, image: Image.Image) -> List[Dict]:
        """
        使用多种预处理方法进行OCR识别，选择最佳结果

        Args:
            image: PIL图像对象

        Returns:
            List[Dict]: 检测到的字符信息列表
        """
        if not self.ocr:
            raise RuntimeError("OCR未正确初始化")

        # 1. 分析图像特征
        print("  分析图像特征...")
        characteristics = self.analyze_image_characteristics(image)
        print(f"    图像尺寸: {characteristics['width']}x{characteristics['height']} ({characteristics['size_category']})")
        print(f"    亮度: {characteristics['brightness']:.2f} ({characteristics['brightness_category']})")
        print(f"    对比度: {characteristics['contrast']:.2f} ({characteristics['contrast_category']})")
        print(f"    噪声: {characteristics['noise_level']:.3f} ({characteristics['noise_category']})")
        print(f"    复杂度: {characteristics['edge_density']:.3f} ({characteristics['complexity']})")
        print(f"    估计字符数: {characteristics['estimated_char_count']}")

        # 2. 获取自适应参数
        adaptive_params = self.get_adaptive_ocr_params(characteristics)
        print(f"  自适应参数: {adaptive_params}")

        preprocessing_methods = [
            ("基础预处理", self.preprocess_image_basic),
            ("高级预处理", self.preprocess_image_advanced),
            ("Otsu阈值", self.preprocess_image_otsu),
            ("PIL增强", self.preprocess_image_pil_enhanced),
            ("验证码优化", self.preprocess_image_captcha_optimized),
            ("多尺度处理", self.preprocess_image_multi_scale),
            ("智能去噪", self.preprocess_image_intelligent_denoise)
        ]

        all_results = []

        for method_name, preprocess_func in preprocessing_methods:
            try:
                print(f"  尝试{method_name}...")
                processed_img = preprocess_func(image)

                # 使用自适应参数执行OCR识别
                results = self._ocr_with_adaptive_params(processed_img, adaptive_params)

                characters = self._parse_ocr_results(results)

                # 计算结果质量分数
                quality_score = self._calculate_quality_score(characters)

                all_results.append({
                    'method': method_name,
                    'characters': characters,
                    'quality_score': quality_score,
                    'processed_image': processed_img,
                    'characteristics': characteristics
                })

                print(f"    {method_name}: 识别到{len(characters)}个字符, 质量分数: {quality_score:.3f}")

            except Exception as e:
                print(f"    {method_name}失败: {e}")
                continue

        if not all_results:
            print("  所有预处理方法都失败了")
            return []

        # 选择质量分数最高的结果
        best_result = max(all_results, key=lambda x: x['quality_score'])
        print(f"  选择最佳方法: {best_result['method']}")

        return best_result['characters']

    def _ocr_with_adaptive_params(self, image: np.ndarray, params: Dict):
        """使用自适应参数执行OCR"""
        # 注意：PaddleOCR的参数在初始化时设定，运行时无法动态修改
        # 这里我们使用默认的OCR，但可以根据参数调整后处理
        try:
            results = self.ocr.ocr(image, cls=True)

            # 根据自适应参数过滤结果
            if results and results[0]:
                filtered_results = []
                for line in results[0]:
                    if len(line) >= 2:
                        bbox = line[0]
                        text_info = line[1]
                        if len(text_info) >= 2:
                            confidence = text_info[1]
                            # 使用自适应的置信度阈值
                            if confidence >= params['drop_score']:
                                filtered_results.append(line)

                return [filtered_results] if filtered_results else [[]]

            return results
        except Exception as e:
            print(f"自适应OCR执行失败: {e}")
            return [[]]

    def detect_characters_with_missing_char_recovery(self, image: Image.Image, required_chars: List[str]) -> List[Dict]:
        """
        增强版字符检测 - 专门处理缺失字符的恢复
        """
        # 首先使用标准方法检测
        standard_chars = self.detect_characters_multi_method(image)

        # 检查哪些字符缺失
        detected_texts = [char['text'] for char in standard_chars]
        missing_chars = []

        for required_char in required_chars:
            found = False
            for detected_char in standard_chars:
                # 使用增强的相似度计算
                matcher = RobustCoordinateMatcher(similarity_threshold=0.3)
                similarity = matcher.calculate_enhanced_similarity(required_char, detected_char['text'])
                if similarity >= 0.7:  # 降低阈值
                    found = True
                    break

            if not found:
                missing_chars.append(required_char)

        if not missing_chars:
            return standard_chars

        print(f"  检测到缺失字符: {missing_chars}")
        print(f"  尝试恢复缺失字符...")

        # 尝试恢复缺失字符
        recovered_chars = self._recover_missing_characters(image, missing_chars, standard_chars)

        # 合并结果
        all_chars = standard_chars + recovered_chars

        print(f"  恢复后总字符数: {len(all_chars)}")

        return all_chars

    def _recover_missing_characters(self, image: Image.Image, missing_chars: List[str], existing_chars: List[Dict]) -> List[Dict]:
        """恢复缺失的字符"""
        recovered = []

        # 策略1: 使用极低置信度阈值重新检测
        print(f"    策略1: 极低置信度检测...")
        low_threshold_chars = self._detect_with_ultra_low_threshold(image)

        for char_info in low_threshold_chars:
            for missing_char in missing_chars:
                matcher = RobustCoordinateMatcher(similarity_threshold=0.2)
                similarity = matcher.calculate_enhanced_similarity(missing_char, char_info['text'])
                if similarity >= 0.5:  # 非常低的阈值
                    print(f"      找到可能的 '{missing_char}': '{char_info['text']}' (相似度: {similarity:.3f})")
                    recovered.append(char_info)
                    break

        # 策略2: 基于位置推断
        print(f"    策略2: 位置推断...")
        inferred_chars = self._infer_missing_by_position(image, missing_chars, existing_chars)
        recovered.extend(inferred_chars)

        # 策略3: 模糊匹配现有字符
        print(f"    策略3: 模糊匹配...")
        fuzzy_matches = self._fuzzy_match_existing_chars(missing_chars, existing_chars)
        recovered.extend(fuzzy_matches)

        return recovered

    def _detect_with_ultra_low_threshold(self, image: Image.Image) -> List[Dict]:
        """使用极低置信度阈值检测字符"""
        try:
            # 转换图像
            img_array = np.array(image)
            if len(img_array.shape) == 3 and img_array.shape[2] == 4:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # 使用极低阈值的OCR
            results = self.ocr.ocr(img_bgr, cls=True)

            characters = []
            if results and results[0]:
                for line in results[0]:
                    if len(line) >= 2:
                        bbox = line[0]
                        text_info = line[1]
                        if len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]

                            # 接受极低置信度的结果
                            if confidence >= 0.1:  # 极低阈值
                                bbox_array = np.array(bbox)
                                center_x = int(np.mean(bbox_array[:, 0]))
                                center_y = int(np.mean(bbox_array[:, 1]))

                                characters.append({
                                    'text': text,
                                    'bbox': bbox,
                                    'confidence': confidence,
                                    'center': (center_x, center_y),
                                    'recovery_method': 'ultra_low_threshold'
                                })

            return characters
        except Exception as e:
            print(f"      极低阈值检测失败: {e}")
            return []

    def _infer_missing_by_position(self, image: Image.Image, missing_chars: List[str], existing_chars: List[Dict]) -> List[Dict]:
        """基于位置推断缺失字符"""
        inferred = []

        if not existing_chars or not missing_chars:
            return inferred

        # 分析现有字符的位置分布
        x_positions = [char['center'][0] for char in existing_chars]
        y_positions = [char['center'][1] for char in existing_chars]

        if len(x_positions) < 2:
            return inferred

        # 计算字符间距
        x_positions.sort()
        avg_spacing = sum(x_positions[i+1] - x_positions[i] for i in range(len(x_positions)-1)) / (len(x_positions)-1)
        avg_y = sum(y_positions) / len(y_positions)

        # 推断可能的位置
        for missing_char in missing_chars:
            # 在字符序列中查找可能的位置
            possible_x = self._estimate_missing_char_position(missing_char, x_positions, avg_spacing)

            if possible_x:
                print(f"      推断 '{missing_char}' 可能位置: ({possible_x}, {int(avg_y)})")
                inferred.append({
                    'text': missing_char,
                    'bbox': [[possible_x-20, avg_y-15], [possible_x+20, avg_y-15],
                            [possible_x+20, avg_y+15], [possible_x-20, avg_y+15]],
                    'confidence': 0.5,  # 推断的置信度
                    'center': (possible_x, int(avg_y)),
                    'recovery_method': 'position_inference'
                })

        return inferred

    def _estimate_missing_char_position(self, missing_char: str, x_positions: List[int], avg_spacing: float) -> Optional[int]:
        """估计缺失字符的位置"""
        # 简单的位置估计逻辑
        if len(x_positions) >= 2:
            # 在现有字符之间或两端寻找空隙
            for i in range(len(x_positions)):
                if i == 0:
                    # 检查左端
                    gap_start = x_positions[i] - avg_spacing
                    if gap_start > 0:
                        return int(gap_start)
                else:
                    # 检查字符间隙
                    gap_size = x_positions[i] - x_positions[i-1]
                    if gap_size > avg_spacing * 1.5:  # 有足够大的间隙
                        return int((x_positions[i] + x_positions[i-1]) / 2)

            # 检查右端
            gap_end = x_positions[-1] + avg_spacing
            return int(gap_end)

        return None

    def _fuzzy_match_existing_chars(self, missing_chars: List[str], existing_chars: List[Dict]) -> List[Dict]:
        """模糊匹配现有字符"""
        fuzzy_matches = []

        for missing_char in missing_chars:
            best_match = None
            best_similarity = 0.0

            for existing_char in existing_chars:
                matcher = RobustCoordinateMatcher(similarity_threshold=0.2)
                similarity = matcher.calculate_enhanced_similarity(missing_char, existing_char['text'])

                if similarity > best_similarity and similarity >= 0.4:  # 降低阈值
                    best_similarity = similarity
                    best_match = existing_char.copy()
                    best_match['original_text'] = best_match['text']
                    best_match['text'] = missing_char  # 替换为目标字符
                    best_match['recovery_method'] = 'fuzzy_match'
                    best_match['fuzzy_similarity'] = similarity

            if best_match:
                print(f"      模糊匹配 '{missing_char}': '{best_match['original_text']}' (相似度: {best_similarity:.3f})")
                fuzzy_matches.append(best_match)

        return fuzzy_matches

    def _parse_ocr_results(self, results) -> List[Dict]:
        """解析OCR结果"""
        characters = []

        if results and results[0]:
            for line in results[0]:
                if len(line) >= 2:
                    bbox = line[0]
                    text_info = line[1]

                    if len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]

                        # 计算中心点坐标
                        bbox_array = np.array(bbox)
                        center_x = int(np.mean(bbox_array[:, 0]))
                        center_y = int(np.mean(bbox_array[:, 1]))

                        # 分割单个字符
                        if len(text) > 1:
                            char_width = (bbox_array[1][0] - bbox_array[0][0]) / len(text)
                            for i, char in enumerate(text):
                                char_center_x = int(bbox_array[0][0] + char_width * (i + 0.5))
                                characters.append({
                                    'text': char,
                                    'bbox': bbox,
                                    'confidence': confidence,
                                    'center': (char_center_x, center_y)
                                })
                        else:
                            characters.append({
                                'text': text,
                                'bbox': bbox,
                                'confidence': confidence,
                                'center': (center_x, center_y)
                            })

        return characters

    def _calculate_quality_score(self, characters: List[Dict]) -> float:
        """计算识别结果的质量分数"""
        if not characters:
            return 0.0

        # 基于字符数量和平均置信度计算分数
        avg_confidence = sum(char['confidence'] for char in characters) / len(characters)
        char_count_score = min(len(characters) / 8.0, 1.0)  # 假设最多8个字符

        # 检查是否包含中文字符
        chinese_char_count = sum(1 for char in characters if self._is_chinese_char(char['text']))
        chinese_ratio = chinese_char_count / len(characters) if characters else 0

        # 综合分数
        quality_score = (avg_confidence * 0.4 +
                        char_count_score * 0.3 +
                        chinese_ratio * 0.3)

        return quality_score

    def _is_chinese_char(self, char: str) -> bool:
        """检查是否为中文字符"""
        return '\u4e00' <= char <= '\u9fff'

    def detect_characters(self, image: Image.Image) -> List[Dict]:
        """
        检测图像中的字符及其位置（主接口）

        Args:
            image: PIL图像对象

        Returns:
            List[Dict]: 检测到的字符信息列表
        """
        print("开始多方法OCR识别...")
        characters = self.detect_characters_multi_method(image)

        # 过滤低置信度字符
        filtered_characters = [char for char in characters if char['confidence'] >= 0.3]

        # 按x坐标排序（从左到右）
        filtered_characters.sort(key=lambda x: x['center'][0])

        print(f"最终识别到 {len(filtered_characters)} 个有效字符")

        return filtered_characters

    def visualize_detection(self, image: Image.Image, characters: List[Dict],
                          output_path: str = None) -> Image.Image:
        """可视化检测结果"""
        img_array = np.array(image)
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
        img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # 绘制检测结果
        for i, char in enumerate(characters):
            bbox = np.array(char['bbox'], dtype=np.int32)
            center = char['center']
            text = char['text']
            confidence = char['confidence']

            # 使用不同颜色区分字符
            colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0),
                     (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)]
            color = colors[i % len(colors)]

            # 绘制边界框
            cv2.polylines(img_cv, [bbox], True, color, 2)

            # 绘制中心点
            cv2.circle(img_cv, center, 5, color, -1)

            # 添加序号和文本标签
            label = f"{i+1}.{text}({confidence:.2f})"
            cv2.putText(img_cv, label, (center[0]-25, center[1]-15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # 转换回PIL格式
        img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
        result_image = Image.fromarray(img_rgb)

        if output_path:
            result_image.save(output_path)
            print(f"增强版OCR可视化结果已保存到: {output_path}")

        return result_image


# ============================================================================
# 强健坐标匹配器类
# ============================================================================

class RobustCoordinateMatcher:
    """强健的坐标匹配器"""

    def __init__(self, similarity_threshold: float = 0.6):
        """
        初始化强健坐标匹配器

        Args:
            similarity_threshold: 字符相似度阈值（降低以增加容错性）
        """
        self.similarity_threshold = similarity_threshold
        self.chinese_char_map = self._build_chinese_char_map()
        self.shape_similarity_map = self._build_shape_similarity_map()
        self.phonetic_similarity_map = self._build_phonetic_similarity_map()
        self.visual_similarity_map = self._build_visual_similarity_map()

    def _build_chinese_char_map(self) -> Dict[str, List[str]]:
        """构建中文字符相似映射表"""
        return {
            '日': ['日', '目', '曰', '白', '百'],
            '无': ['无', '元', '天', '大', '太'],
            '逊': ['逊', '孙', '损', '迅', '讯'],
            '莽': ['莽', '茫', '盲', '忙', '芒'],
            '其': ['其', '期', '棋', '旗', '欺'],
            '他': ['他', '她', '它', '地', '池'],
            '字': ['字', '学', '宇', '守', '宁'],
            '符': ['符', '付', '府', '附', '富'],
            '验': ['验', '检', '险', '脸', '验'],
            '证': ['证', '正', '征', '整', '政'],
            '码': ['码', '马', '妈', '吗', '玛'],
            '识': ['识', '试', '式', '视', '识'],
            '别': ['别', '列', '烈', '裂', '别'],
            '点': ['点', '店', '典', '电', '占'],
            '击': ['击', '系', '计', '记', '击'],
            '按': ['按', '安', '岸', '案', '按'],
            '顺': ['顺', '须', '需', '项', '顺'],
            '序': ['序', '叙', '绪', '续', '序']
        }

    def _build_shape_similarity_map(self) -> Dict[str, List[str]]:
        """构建字形相似映射表"""
        return {
            '日': ['目', '曰', '白', '百'],
            '无': ['元', '天', '大', '太'],
            '逊': ['孙', '损', '迅', '讯'],
            '莽': ['茫', '盲', '忙', '芒'],
            '其': ['期', '棋', '旗', '欺'],
            '他': ['她', '它', '地', '池'],
            '字': ['学', '宇', '守', '字'],
            '符': ['付', '府', '附', '富']
        }

    def _build_phonetic_similarity_map(self) -> Dict[str, List[str]]:
        """构建拼音相似映射表"""
        return {
            '蜡': ['腊', '辣', '拉'],
            '逊': ['孙', '损', '迅'],
            '冰': ['兵', '丙', '病'],
            '河': ['何', '和', '合'],
            '封': ['风', '峰', '锋'],
            '滔': ['涛', '桃', '陶'],
            '试': ['是', '事', '市']
        }

    def _build_visual_similarity_map(self) -> Dict[str, List[str]]:
        """构建视觉相似映射表（基于OCR常见错误）"""
        return {
            '0': ['O', 'o', '○'],
            '1': ['l', 'I', '|'],
            '2': ['Z', 'z'],
            '5': ['S', 's'],
            '6': ['G', 'g'],
            '8': ['B', 'b'],
            '9': ['g', 'q'],
            'A': ['4', 'a'],
            'B': ['8', 'b'],
            'C': ['c', '('],
            'D': ['d', '0'],
            'E': ['e', '3'],
            'F': ['f', 'P'],
            'G': ['g', '6'],
            'H': ['h', '#'],
            'I': ['i', '1', 'l'],
            'J': ['j'],
            'K': ['k'],
            'L': ['l', '1', 'I'],
            'M': ['m', 'W'],
            'N': ['n'],
            'O': ['o', '0', '○'],
            'P': ['p', 'F'],
            'Q': ['q', '9'],
            'R': ['r'],
            'S': ['s', '5'],
            'T': ['t', '7'],
            'U': ['u', 'V'],
            'V': ['v', 'U'],
            'W': ['w', 'M'],
            'X': ['x'],
            'Y': ['y'],
            'Z': ['z', '2']
        }

    def calculate_enhanced_similarity(self, char1: str, char2: str) -> float:
        """
        计算超级增强的字符相似度（多维度分析）

        Args:
            char1: 字符1
            char2: 字符2

        Returns:
            float: 相似度分数 (0-1)
        """
        if char1 == char2:
            return 1.0

        # 多维度相似度计算
        similarities = []

        # 1. 中文字符映射相似度 (权重: 0.35)
        chinese_sim = self._calculate_chinese_similarity(char1, char2)
        if chinese_sim > 0:
            similarities.append(('chinese', chinese_sim, 0.35))

        # 2. 字形相似度 (权重: 0.25)
        shape_sim = self._calculate_shape_similarity(char1, char2)
        if shape_sim > 0:
            similarities.append(('shape', shape_sim, 0.25))

        # 3. 拼音相似度 (权重: 0.2)
        phonetic_sim = self._calculate_phonetic_similarity(char1, char2)
        if phonetic_sim > 0:
            similarities.append(('phonetic', phonetic_sim, 0.2))

        # 4. 视觉相似度 (权重: 0.15)
        visual_sim = self._calculate_visual_similarity(char1, char2)
        if visual_sim > 0:
            similarities.append(('visual', visual_sim, 0.15))

        # 5. 部首相似度 (权重: 0.1)
        radical_sim = self._calculate_radical_similarity(char1, char2)
        if radical_sim > 0:
            similarities.append(('radical', radical_sim, 0.1))

        # 6. 编辑距离相似度 (权重: 0.05)
        edit_sim = self._calculate_edit_distance_similarity(char1, char2)
        similarities.append(('edit', edit_sim, 0.05))

        # 7. 特殊OCR错误模式 (权重: 0.3)
        ocr_error_sim = self._calculate_ocr_error_similarity(char1, char2)
        if ocr_error_sim > 0:
            similarities.append(('ocr_error', ocr_error_sim, 0.3))

        if not similarities:
            return 0.0

        # 加权平均计算
        total_weight = sum(weight for _, _, weight in similarities)
        weighted_sum = sum(sim * weight for _, sim, weight in similarities)

        final_similarity = weighted_sum / total_weight if total_weight > 0 else 0.0

        # 应用置信度调整
        confidence_factor = self._calculate_confidence_factor(char1, char2, similarities)
        final_similarity *= confidence_factor

        return min(1.0, final_similarity)

    def _calculate_chinese_similarity(self, char1: str, char2: str) -> float:
        """计算中文字符相似度"""
        for target_char, similar_chars in self.chinese_char_map.items():
            if (char1 == target_char and char2 in similar_chars) or \
               (char2 == target_char and char1 in similar_chars):
                return 0.95
        return 0.0

    def _calculate_shape_similarity(self, char1: str, char2: str) -> float:
        """计算字形相似度"""
        for target_char, similar_chars in self.shape_similarity_map.items():
            if (char1 == target_char and char2 in similar_chars) or \
               (char2 == target_char and char1 in similar_chars):
                return 0.85
        return 0.0

    def _calculate_phonetic_similarity(self, char1: str, char2: str) -> float:
        """计算拼音相似度"""
        for target_char, similar_chars in self.phonetic_similarity_map.items():
            if (char1 == target_char and char2 in similar_chars) or \
               (char2 == target_char and char1 in similar_chars):
                return 0.8
        return 0.0

    def _calculate_visual_similarity(self, char1: str, char2: str) -> float:
        """计算视觉相似度"""
        for target_char, similar_chars in self.visual_similarity_map.items():
            if (char1 == target_char and char2 in similar_chars) or \
               (char2 == target_char and char1 in similar_chars):
                return 0.9
        return 0.0

    def _calculate_edit_distance_similarity(self, char1: str, char2: str) -> float:
        """计算编辑距离相似度"""
        return difflib.SequenceMatcher(None, char1, char2).ratio()

    def _calculate_ocr_error_similarity(self, char1: str, char2: str) -> float:
        """计算OCR常见错误相似度"""
        # 常见的OCR识别错误模式（扩展版）
        ocr_error_patterns = [
            ('蜡', '腊'), ('腊', '蜡'),
            ('逊', '孙'), ('孙', '逊'),
            ('冰', '兵'), ('兵', '冰'),
            ('冰', '酒'), ('酒', '冰'),  # 新增：冰可能被识别为酒
            ('冰', '永'), ('永', '冰'),  # 新增：冰可能被识别为永
            ('冰', '水'), ('水', '冰'),  # 新增：冰可能被识别为水
            ('河', '何'), ('何', '河'),
            ('封', '风'), ('风', '封'),
            ('滔', '涛'), ('涛', '滔'),
            ('试', '是'), ('是', '试'),
            ('试', '式'), ('式', '试'),  # 新增：试可能被识别为式
            ('日', '目'), ('目', '日'),
            ('无', '元'), ('元', '无'),
            ('其', '期'), ('期', '其'),
            ('他', '她'), ('她', '他'),
            ('字', '学'), ('学', '字'),
            ('符', '付'), ('付', '符'),
            # 添加更多基于实际测试的错误模式
            ('蜡', '腊'), ('腊', '蜡'),
            ('蜡', '辣'), ('辣', '蜡'),
            ('逊', '迅'), ('迅', '逊'),
            ('逊', '损'), ('损', '逊'),
            ('河', '合'), ('合', '河'),
            ('河', '和'), ('和', '河'),
            ('封', '峰'), ('峰', '封'),
            ('封', '锋'), ('锋', '封')
        ]

        if (char1, char2) in ocr_error_patterns:
            return 0.95  # 提高相似度分数

        return 0.0

    def _calculate_confidence_factor(self, char1: str, char2: str, similarities: List) -> float:
        """计算置信度因子"""
        # 如果有多个维度匹配，增加置信度
        match_count = len([s for s in similarities if s[1] > 0.7])

        if match_count >= 3:
            return 1.1  # 多维度匹配，增加置信度
        elif match_count >= 2:
            return 1.05
        else:
            return 1.0

    def _calculate_radical_similarity(self, char1: str, char2: str) -> float:
        """计算部首相似性（简化版）"""
        # 简化的部首检查
        common_radicals = {
            '日': ['日', '目', '曰'],
            '无': ['无', '元'],
            '辶': ['逊', '迅', '运'],
            '艹': ['莽', '茫', '芒'],
            '其': ['其', '期'],
            '亻': ['他', '她', '付']
        }

        for radical, chars in common_radicals.items():
            if char1 in chars and char2 in chars:
                return 0.15

        return 0.0

    def find_character_matches_robust(self, required_chars: List[str],
                                    detected_chars: List[Dict]) -> Dict[str, List[Dict]]:
        """
        为每个需要的字符找到可能的匹配（强健版）

        Args:
            required_chars: 需要点击的字符列表
            detected_chars: OCR检测到的字符列表

        Returns:
            Dict[str, List[Dict]]: 每个需要字符对应的可能匹配列表
        """
        matches = defaultdict(list)

        for required_char in required_chars:
            for detected_char in detected_chars:
                similarity = self.calculate_enhanced_similarity(
                    required_char, detected_char['text']
                )

                if similarity >= self.similarity_threshold:
                    match_info = detected_char.copy()
                    match_info['similarity'] = similarity
                    match_info['match_type'] = self._get_match_type(similarity)
                    matches[required_char].append(match_info)

        # 按相似度排序
        for char in matches:
            matches[char].sort(key=lambda x: x['similarity'], reverse=True)

        return dict(matches)

    def _get_match_type(self, similarity: float) -> str:
        """获取匹配类型"""
        if similarity >= 0.95:
            return "完全匹配"
        elif similarity >= 0.8:
            return "高相似度"
        elif similarity >= 0.6:
            return "中等相似度"
        else:
            return "低相似度"

    def resolve_conflicts_advanced(self, matches: Dict[str, List[Dict]]) -> Dict[str, Dict]:
        """
        高级冲突解决算法

        Args:
            matches: 字符匹配结果

        Returns:
            Dict[str, Dict]: 解决冲突后的最终匹配结果
        """
        # 记录每个检测字符的使用情况
        detected_char_usage = defaultdict(list)

        for required_char, match_list in matches.items():
            for match in match_list:
                detected_char_usage[match['text']].append({
                    'required_char': required_char,
                    'match_info': match
                })

        final_matches = {}
        used_detected_chars = set()

        # 第一轮：处理唯一匹配和高置信度匹配
        for required_char, match_list in matches.items():
            if not match_list:
                continue

            best_match = match_list[0]
            detected_text = best_match['text']

            # 如果是唯一匹配或高相似度匹配（降低阈值）
            if (len(detected_char_usage[detected_text]) == 1 or
                best_match['similarity'] >= 0.7):  # 降低阈值从0.9到0.7
                final_matches[required_char] = best_match
                used_detected_chars.add(detected_text)

        # 第二轮：处理剩余的冲突
        for detected_text, usage_list in detected_char_usage.items():
            if detected_text in used_detected_chars:
                continue

            if len(usage_list) > 1:
                # 多个需要字符匹配到同一个检测字符
                # 选择综合分数最高的
                best_usage = max(usage_list, key=lambda x:
                    x['match_info']['similarity'] * x['match_info']['confidence'])

                required_char = best_usage['required_char']
                if required_char not in final_matches:
                    final_matches[required_char] = best_usage['match_info']
                    used_detected_chars.add(detected_text)

        # 第三轮：为未匹配的字符寻找替代方案
        for required_char, match_list in matches.items():
            if required_char not in final_matches and match_list:
                for match in match_list:
                    if match['text'] not in used_detected_chars:
                        final_matches[required_char] = match
                        used_detected_chars.add(match['text'])
                        break

        return final_matches

    def get_ordered_coordinates(self, required_chars: List[str],
                              detected_chars: List[Dict]) -> List[Optional[Tuple[int, int]]]:
        """
        获取按顺序排列的坐标列表，确保坐标保存的可靠性

        Args:
            required_chars: 需要点击的字符列表（按顺序）
            detected_chars: OCR检测到的字符列表

        Returns:
            List[Optional[Tuple[int, int]]]: 按顺序的坐标列表，未匹配的为None
        """
        # 找到字符匹配
        matches = self.find_character_matches_robust(required_chars, detected_chars)

        # 解决冲突
        final_matches = self.resolve_conflicts_advanced(matches)

        # 按required_chars的顺序生成坐标列表
        coordinates = []
        for required_char in required_chars:
            if required_char in final_matches:
                match = final_matches[required_char]
                coordinates.append(match['center'])
            else:
                coordinates.append(None)

        return coordinates

    def get_detailed_results_robust(self, required_chars: List[str],
                                  detected_chars: List[Dict]) -> Dict:
        """
        获取详细的强健匹配结果

        Args:
            required_chars: 需要点击的字符列表
            detected_chars: OCR检测到的字符列表

        Returns:
            Dict: 详细的匹配结果
        """
        matches = self.find_character_matches_robust(required_chars, detected_chars)
        final_matches = self.resolve_conflicts_advanced(matches)
        coordinates = self.get_ordered_coordinates(required_chars, detected_chars)

        # 统计信息
        matched_count = len([c for c in coordinates if c is not None])
        total_count = len(required_chars)
        success_rate = matched_count / total_count if total_count > 0 else 0

        # 匹配质量分析
        match_quality = self._analyze_match_quality(final_matches)

        return {
            'required_chars': required_chars,
            'detected_chars': detected_chars,
            'matches': dict(matches),
            'final_matches': final_matches,
            'coordinates': coordinates,
            'statistics': {
                'total_required': total_count,
                'matched_count': matched_count,
                'success_rate': success_rate,
                'match_quality': match_quality
            }
        }

    def _analyze_match_quality(self, final_matches: Dict[str, Dict]) -> Dict:
        """分析匹配质量"""
        if not final_matches:
            return {'average_similarity': 0, 'average_confidence': 0, 'quality_distribution': {}}

        similarities = [match['similarity'] for match in final_matches.values()]
        confidences = [match['confidence'] for match in final_matches.values()]

        # 质量分布
        quality_distribution = {'完全匹配': 0, '高相似度': 0, '中等相似度': 0, '低相似度': 0}
        for match in final_matches.values():
            quality_distribution[match['match_type']] += 1

        return {
            'average_similarity': sum(similarities) / len(similarities),
            'average_confidence': sum(confidences) / len(confidences),
            'quality_distribution': quality_distribution
        }


# ============================================================================
# 优化版CAPTCHA解决器主类
# ============================================================================

class OptimizedCaptchaSolver:
    """优化版CAPTCHA验证码解决器"""

    def __init__(self, use_gpu: bool = False, similarity_threshold: float = 0.6):
        """
        初始化优化版CAPTCHA解决器

        Args:
            use_gpu: 是否使用GPU加速OCR
            similarity_threshold: 字符匹配相似度阈值（降低以增加容错性）
        """
        self.html_parser = None
        self.ocr_detector = EnhancedOCRDetector(use_gpu=use_gpu)
        self.coordinate_matcher = RobustCoordinateMatcher(similarity_threshold=similarity_threshold)

        print("优化版CAPTCHA解决器初始化完成")
        print(f"GPU加速: {'启用' if use_gpu else '禁用'}")
        print(f"相似度阈值: {similarity_threshold}")

    def solve_captcha_optimized(self, html_file_path: str,
                               save_debug_images: bool = True) -> Dict:
        """
        优化版CAPTCHA解决方案
        
        Args:
            html_file_path: HTML文件路径
            save_debug_images: 是否保存调试图像
            
        Returns:
            Dict: 解决结果，包含坐标列表和详细信息
        """
        print(f"开始处理CAPTCHA: {html_file_path}")
        print("=" * 50)
        
        # 1. 解析HTML文件
        print("步骤1: 解析HTML文件...")
        self.html_parser = CaptchaHTMLParser(html_file_path)
        captcha_image, required_chars = self.html_parser.get_captcha_info()
        
        if not captcha_image:
            return {
                'success': False,
                'error': '无法从HTML文件中提取CAPTCHA图像',
                'coordinates': [],
                'details': {}
            }
        
        if not required_chars:
            return {
                'success': False,
                'error': '无法从HTML文件中提取字符指令',
                'coordinates': [],
                'details': {}
            }
        
        print(f"  ✓ 成功提取CAPTCHA图像 (尺寸: {captcha_image.size})")
        print(f"  ✓ 需要点击的字符序列: {required_chars}")
        
        # 保存原始图像
        if save_debug_images:
            captcha_image.save('optimized_captcha_original.png')
            print("  ✓ 原始图像已保存: optimized_captcha_original.png")
        
        # 2. 增强版OCR字符识别
        print("\n步骤2: 增强版OCR字符识别...")
        detected_chars = self.ocr_detector.detect_characters(captcha_image)
        
        if not detected_chars:
            return {
                'success': False,
                'error': '增强版OCR未能识别出任何字符',
                'coordinates': [],
                'details': {
                    'required_chars': required_chars,
                    'detected_chars': []
                }
            }
        
        print(f"\n  ✓ 最终识别到 {len(detected_chars)} 个字符:")
        for i, char in enumerate(detected_chars):
            print(f"    {i+1}. '{char['text']}' - 坐标: {char['center']}, "
                  f"置信度: {char['confidence']:.3f}")
        
        # 保存OCR可视化结果
        if save_debug_images:
            self.ocr_detector.visualize_detection(
                captcha_image, detected_chars, 'optimized_ocr_result.png'
            )
            print("  ✓ OCR结果可视化已保存: optimized_ocr_result.png")
        
        # 3. 强健字符坐标匹配
        print("\n步骤3: 强健字符坐标匹配...")
        matching_results = self.coordinate_matcher.get_detailed_results_robust(
            required_chars, detected_chars
        )
        
        coordinates = matching_results['coordinates']
        success_rate = matching_results['statistics']['success_rate']
        match_quality = matching_results['statistics']['match_quality']
        
        print(f"  ✓ 匹配成功率: {success_rate:.1%}")
        print(f"  ✓ 平均相似度: {match_quality['average_similarity']:.3f}")
        print(f"  ✓ 平均置信度: {match_quality['average_confidence']:.3f}")
        
        # 4. 生成有序坐标结果
        success = success_rate >= 0.5  # 降低成功阈值，增加容错性
        
        result = {
            'success': success,
            'coordinates': coordinates,
            'required_chars': required_chars,
            'click_sequence': [],
            'details': matching_results,
            'statistics': {
                'total_chars': len(required_chars),
                'matched_chars': matching_results['statistics']['matched_count'],
                'success_rate': success_rate,
                'ocr_chars_detected': len(detected_chars),
                'match_quality': match_quality
            }
        }
        
        # 生成有序点击序列
        for i, (char, coord) in enumerate(zip(required_chars, coordinates)):
            if coord:
                # 找到对应的匹配信息
                match_info = matching_results['final_matches'].get(char, {})
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': coord,
                    'x': coord[0],
                    'y': coord[1],
                    'matched_char': match_info.get('text', ''),
                    'similarity': match_info.get('similarity', 0),
                    'confidence': match_info.get('confidence', 0),
                    'match_type': match_info.get('match_type', '')
                })
            else:
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': None,
                    'error': '未找到匹配字符'
                })
        
        return result

    def solve_captcha_from_html_content(self, html_content: str,
                                       save_debug_images: bool = True) -> Dict:
        """
        从HTML文本内容解决CAPTCHA验证码

        Args:
            html_content: HTML文本内容
            save_debug_images: 是否保存调试图像

        Returns:
            Dict: 解决结果，包含坐标列表和详细信息
        """
        print(f"开始处理HTML内容...")
        print("=" * 50)

        # 1. 解析HTML内容
        print("步骤1: 解析HTML内容...")
        self.html_parser = CaptchaHTMLParser(html_content, is_file_path=False)
        captcha_image, required_chars = self.html_parser.get_captcha_info()

        if not captcha_image:
            return {
                'success': False,
                'error': '无法从HTML内容中提取CAPTCHA图像',
                'coordinates': [],
                'details': {}
            }

        if not required_chars:
            return {
                'success': False,
                'error': '无法从HTML内容中提取字符指令',
                'coordinates': [],
                'details': {}
            }

        print(f"  ✓ 成功提取CAPTCHA图像 (尺寸: {captcha_image.size})")
        print(f"  ✓ 需要点击的字符序列: {required_chars}")

        # 保存原始图像
        if save_debug_images:
            captcha_image.save('html_content_captcha_original.png')
            print("  ✓ 原始图像已保存: html_content_captcha_original.png")

        # 2. 增强版OCR字符识别
        print("\n步骤2: 增强版OCR字符识别...")
        detected_chars = self.ocr_detector.detect_characters(captcha_image)

        if not detected_chars:
            return {
                'success': False,
                'error': '增强版OCR未能识别出任何字符',
                'coordinates': [],
                'details': {
                    'required_chars': required_chars,
                    'detected_chars': []
                }
            }

        print(f"\n  ✓ 最终识别到 {len(detected_chars)} 个字符:")
        for i, char in enumerate(detected_chars):
            print(f"    {i+1}. '{char['text']}' - 坐标: {char['center']}, "
                  f"置信度: {char['confidence']:.3f}")

        # 保存OCR可视化结果
        if save_debug_images:
            self.ocr_detector.visualize_detection(
                captcha_image, detected_chars, 'html_content_ocr_result.png'
            )
            print("  ✓ OCR结果可视化已保存: html_content_ocr_result.png")

        # 3. 强健字符坐标匹配
        print("\n步骤3: 强健字符坐标匹配...")
        matching_results = self.coordinate_matcher.get_detailed_results_robust(
            required_chars, detected_chars
        )

        coordinates = matching_results['coordinates']
        success_rate = matching_results['statistics']['success_rate']
        match_quality = matching_results['statistics']['match_quality']

        print(f"  ✓ 匹配成功率: {success_rate:.1%}")
        print(f"  ✓ 平均相似度: {match_quality['average_similarity']:.3f}")
        print(f"  ✓ 平均置信度: {match_quality['average_confidence']:.3f}")

        # 4. 生成有序坐标结果
        success = success_rate >= 0.5  # 降低成功阈值，增加容错性

        result = {
            'success': success,
            'coordinates': coordinates,
            'required_chars': required_chars,
            'click_sequence': [],
            'details': matching_results,
            'statistics': {
                'total_chars': len(required_chars),
                'matched_chars': matching_results['statistics']['matched_count'],
                'success_rate': success_rate,
                'ocr_chars_detected': len(detected_chars),
                'match_quality': match_quality
            }
        }

        # 生成有序点击序列
        for i, (char, coord) in enumerate(zip(required_chars, coordinates)):
            if coord:
                # 找到对应的匹配信息
                match_info = matching_results['final_matches'].get(char, {})
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': coord,
                    'x': coord[0],
                    'y': coord[1],
                    'matched_char': match_info.get('text', ''),
                    'similarity': match_info.get('similarity', 0),
                    'confidence': match_info.get('confidence', 0),
                    'match_type': match_info.get('match_type', '')
                })
            else:
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': None,
                    'error': '未找到匹配字符'
                })

        return result

    def get_ordered_coordinates_only(self, html_file_path: str) -> List[Optional[Tuple[int, int]]]:
        """
        简化接口：只返回有序的点击坐标列表
        
        Args:
            html_file_path: HTML文件路径
            
        Returns:
            List[Optional[Tuple[int, int]]]: 按顺序的坐标列表，失败的位置为None
        """
        result = self.solve_captcha_optimized(html_file_path, save_debug_images=False)
        return result.get('coordinates', [])
    
    def print_optimized_solution(self, result: Dict):
        """
        打印优化版解决方案
        
        Args:
            result: solve_captcha_optimized返回的结果
        """
        print("\n" + "="*60)
        print("优化版CAPTCHA解决方案")
        print("="*60)
        
        if result['success']:
            print("✅ 解决成功!")
            print(f"需要点击的字符序列: {result['required_chars']}")
            print(f"OCR检测到字符数: {result['statistics']['ocr_chars_detected']}")
            print(f"成功匹配字符数: {result['statistics']['matched_chars']}")
            
            print("\n📍 有序点击步骤:")
            for step in result['click_sequence']:
                if step.get('coordinate'):
                    print(f"  第{step['step']}步: 点击字符 '{step['character']}' "
                          f"坐标 ({step['x']}, {step['y']})")
                    print(f"         匹配到: '{step['matched_char']}' "
                          f"(相似度: {step['similarity']:.3f}, "
                          f"置信度: {step['confidence']:.3f}, "
                          f"{step['match_type']})")
                else:
                    print(f"  第{step['step']}步: 字符 '{step['character']}' - "
                          f"❌ {step.get('error', '未知错误')}")
            
            print(f"\n📊 统计信息:")
            stats = result['statistics']
            quality = stats['match_quality']
            print(f"  总字符数: {stats['total_chars']}")
            print(f"  匹配成功: {stats['matched_chars']}")
            print(f"  成功率: {stats['success_rate']:.1%}")
            print(f"  平均相似度: {quality['average_similarity']:.3f}")
            print(f"  平均置信度: {quality['average_confidence']:.3f}")
            print(f"  匹配质量分布: {quality['quality_distribution']}")
            
        else:
            print("❌ 解决失败!")
            if 'error' in result:
                print(f"错误信息: {result['error']}")
        
        print("="*60)
    
    def validate_coordinates(self, coordinates: List[Optional[Tuple[int, int]]], 
                           image_size: Tuple[int, int]) -> List[bool]:
        """
        验证坐标的有效性
        
        Args:
            coordinates: 坐标列表
            image_size: 图像尺寸 (width, height)
            
        Returns:
            List[bool]: 每个坐标是否有效
        """
        width, height = image_size
        validity = []
        
        for coord in coordinates:
            if coord is None:
                validity.append(False)
            else:
                x, y = coord
                # 检查坐标是否在图像范围内
                valid = (0 <= x <= width and 0 <= y <= height)
                validity.append(valid)
        
        return validity
    
    def export_coordinates_to_file(self, result: Dict, output_file: str = 'captcha_coordinates.txt'):
        """
        导出坐标到文件
        
        Args:
            result: 解决结果
            output_file: 输出文件路径
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("CAPTCHA坐标解决结果\n")
                f.write("=" * 30 + "\n")
                f.write(f"字符序列: {result['required_chars']}\n")
                f.write(f"成功率: {result['statistics']['success_rate']:.1%}\n\n")
                
                f.write("有序坐标列表:\n")
                for i, coord in enumerate(result['coordinates']):
                    char = result['required_chars'][i]
                    if coord:
                        f.write(f"{i+1}. '{char}': ({coord[0]}, {coord[1]})\n")
                    else:
                        f.write(f"{i+1}. '{char}': 未找到\n")
                
                f.write(f"\n详细匹配信息:\n")
                for step in result['click_sequence']:
                    if step.get('coordinate'):
                        f.write(f"第{step['step']}步: '{step['character']}' -> "
                               f"'{step['matched_char']}' "
                               f"坐标({step['x']}, {step['y']}) "
                               f"相似度{step['similarity']:.3f}\n")
            
            print(f"✓ 坐标结果已导出到: {output_file}")
            
        except Exception as e:
            print(f"导出坐标失败: {e}")


# ============================================================================
# 公共API函数 - 用于模块导入
# ============================================================================

def solve_captcha_from_html(html_content: str, use_gpu: bool = False,
                           similarity_threshold: float = 0.6,
                           save_debug_images: bool = False,
                           verbose: bool = False) -> Dict:
    """
    从HTML文本内容解决CAPTCHA验证码的公共API函数

    Args:
        html_content: HTML文本内容
        use_gpu: 是否使用GPU加速OCR (默认: False)
        similarity_threshold: 字符匹配相似度阈值 (默认: 0.6)
        save_debug_images: 是否保存调试图像 (默认: False)
        verbose: 是否显示详细输出 (默认: False)

    Returns:
        Dict: 包含以下键的结果字典:
            - success (bool): 是否成功解决
            - coordinates (List[Optional[Tuple[int, int]]]): 有序坐标列表
            - required_chars (List[str]): 需要点击的字符序列
            - click_sequence (List[Dict]): 详细的点击步骤信息
            - statistics (Dict): 统计信息
            - error (str, optional): 错误消息（如果失败）
            - processing_time (float): 处理时间（秒）

    Example:
        >>> html = '<div>请依次点击【日,无,逊,莽】</div><img src="data:image/png;base64,..." />'
        >>> result = solve_captcha_from_html(html)
        >>> if result['success']:
        ...     coordinates = result['coordinates']
        ...     for i, coord in enumerate(coordinates):
        ...         if coord:
        ...             print(f"点击字符 {result['required_chars'][i]} 坐标: {coord}")
    """
    if not html_content or not html_content.strip():
        return {
            'success': False,
            'coordinates': [],
            'required_chars': [],
            'click_sequence': [],
            'statistics': {},
            'error': 'HTML内容为空',
            'processing_time': 0.0
        }

    try:
        # 创建解决器（静默模式）
        if not verbose:
            # 临时重定向stdout来抑制初始化输出
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

        solver = OptimizedCaptchaSolver(
            use_gpu=use_gpu,
            similarity_threshold=similarity_threshold
        )

        if not verbose:
            sys.stdout = old_stdout

        # 解决CAPTCHA
        start_time = time.time()
        result = solver.solve_captcha_from_html_content(html_content, save_debug_images=save_debug_images)
        end_time = time.time()

        # 添加处理时间
        result['processing_time'] = end_time - start_time

        if verbose and result['success']:
            print(f"✅ CAPTCHA解决成功!")
            print(f"字符序列: {result['required_chars']}")
            print(f"成功率: {result['statistics']['success_rate']:.1%}")
            print(f"处理时间: {result['processing_time']:.2f}秒")

        return result

    except Exception as e:
        return {
            'success': False,
            'coordinates': [],
            'required_chars': [],
            'click_sequence': [],
            'statistics': {},
            'error': f'处理过程中发生错误: {str(e)}',
            'processing_time': 0.0
        }


def solve_captcha(html_file_path: str, use_gpu: bool = False,
                 similarity_threshold: float = 0.6,
                 save_debug_images: bool = False,
                 verbose: bool = False) -> Dict:
    """
    解决CAPTCHA验证码的公共API函数

    Args:
        html_file_path: HTML文件路径
        use_gpu: 是否使用GPU加速OCR (默认: False)
        similarity_threshold: 字符匹配相似度阈值 (默认: 0.6)
        save_debug_images: 是否保存调试图像 (默认: False)
        verbose: 是否显示详细输出 (默认: False)

    Returns:
        Dict: 包含以下键的结果字典:
            - success (bool): 是否成功解决
            - coordinates (List[Optional[Tuple[int, int]]]): 有序坐标列表
            - required_chars (List[str]): 需要点击的字符序列
            - click_sequence (List[Dict]): 详细的点击步骤信息
            - statistics (Dict): 统计信息
            - error (str, optional): 错误消息（如果失败）
            - processing_time (float): 处理时间（秒）

    Example:
        >>> result = solve_captcha('1.html')
        >>> if result['success']:
        ...     coordinates = result['coordinates']
        ...     for i, coord in enumerate(coordinates):
        ...         if coord:
        ...             print(f"点击字符 {result['required_chars'][i]} 坐标: {coord}")
    """
    if not os.path.exists(html_file_path):
        return {
            'success': False,
            'coordinates': [],
            'required_chars': [],
            'click_sequence': [],
            'statistics': {},
            'error': f'HTML文件不存在: {html_file_path}',
            'processing_time': 0.0
        }

    try:
        # 创建解决器（静默模式）
        if not verbose:
            # 临时重定向stdout来抑制初始化输出
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

        solver = OptimizedCaptchaSolver(
            use_gpu=use_gpu,
            similarity_threshold=similarity_threshold
        )

        if not verbose:
            sys.stdout = old_stdout

        # 解决CAPTCHA
        start_time = time.time()
        result = solver.solve_captcha_optimized(html_file_path, save_debug_images=save_debug_images)
        end_time = time.time()

        # 添加处理时间
        result['processing_time'] = end_time - start_time

        if verbose and result['success']:
            print(f"✅ CAPTCHA解决成功!")
            print(f"字符序列: {result['required_chars']}")
            print(f"成功率: {result['statistics']['success_rate']:.1%}")
            print(f"处理时间: {result['processing_time']:.2f}秒")

        return result

    except Exception as e:
        return {
            'success': False,
            'coordinates': [],
            'required_chars': [],
            'click_sequence': [],
            'statistics': {},
            'error': f'处理过程中发生错误: {str(e)}',
            'processing_time': 0.0
        }


def get_coordinates_from_html(html_content: str, **kwargs) -> List[Optional[Tuple[int, int]]]:
    """
    从HTML文本内容获取坐标列表的简化API

    Args:
        html_content: HTML文本内容
        **kwargs: 传递给solve_captcha_from_html的其他参数

    Returns:
        List[Optional[Tuple[int, int]]]: 有序坐标列表，失败的位置为None

    Example:
        >>> html = '<div>请依次点击【日,无,逊,莽】</div><img src="data:image/png;base64,..." />'
        >>> coordinates = get_coordinates_from_html(html)
        >>> print(f"坐标列表: {coordinates}")
    """
    result = solve_captcha_from_html(html_content, **kwargs)
    return result.get('coordinates', [])


def get_coordinates_only(html_file_path: str, **kwargs) -> List[Optional[Tuple[int, int]]]:
    """
    简化API：只返回坐标列表

    Args:
        html_file_path: HTML文件路径
        **kwargs: 传递给solve_captcha的其他参数

    Returns:
        List[Optional[Tuple[int, int]]]: 有序坐标列表，失败的位置为None

    Example:
        >>> coordinates = get_coordinates_only('1.html')
        >>> print(f"坐标列表: {coordinates}")
    """
    result = solve_captcha(html_file_path, **kwargs)
    return result.get('coordinates', [])


def extract_captcha_info_from_html(html_content: str) -> Dict:
    """
    从HTML文本内容提取CAPTCHA信息而不进行OCR处理

    Args:
        html_content: HTML文本内容

    Returns:
        Dict: 包含以下键的信息字典:
            - success (bool): 是否成功提取
            - image_size (Tuple[int, int], optional): 图像尺寸
            - required_chars (List[str]): 需要点击的字符序列
            - instruction_text (str): 原始指令文本
            - error (str, optional): 错误消息（如果失败）
    """
    if not html_content or not html_content.strip():
        return {
            'success': False,
            'required_chars': [],
            'instruction_text': '',
            'error': 'HTML内容为空'
        }

    try:
        parser = CaptchaHTMLParser(html_content, is_file_path=False)
        captcha_image, required_chars = parser.get_captcha_info()

        if not captcha_image:
            return {
                'success': False,
                'required_chars': [],
                'instruction_text': parser.instruction_text,
                'error': '无法从HTML内容中提取CAPTCHA图像'
            }

        if not required_chars:
            return {
                'success': False,
                'image_size': captcha_image.size,
                'required_chars': [],
                'instruction_text': parser.instruction_text,
                'error': '无法从HTML内容中提取字符指令'
            }

        return {
            'success': True,
            'image_size': captcha_image.size,
            'required_chars': required_chars,
            'instruction_text': parser.instruction_text
        }

    except Exception as e:
        return {
            'success': False,
            'required_chars': [],
            'instruction_text': '',
            'error': f'解析过程中发生错误: {str(e)}'
        }


def extract_captcha_info(html_file_path: str) -> Dict:
    """
    仅提取CAPTCHA信息而不进行OCR处理

    Args:
        html_file_path: HTML文件路径

    Returns:
        Dict: 包含以下键的信息字典:
            - success (bool): 是否成功提取
            - image_size (Tuple[int, int], optional): 图像尺寸
            - required_chars (List[str]): 需要点击的字符序列
            - instruction_text (str): 原始指令文本
            - error (str, optional): 错误消息（如果失败）
    """
    if not os.path.exists(html_file_path):
        return {
            'success': False,
            'required_chars': [],
            'instruction_text': '',
            'error': f'HTML文件不存在: {html_file_path}'
        }

    try:
        parser = CaptchaHTMLParser(html_file_path, is_file_path=True)
        captcha_image, required_chars = parser.get_captcha_info()

        if not captcha_image:
            return {
                'success': False,
                'required_chars': [],
                'instruction_text': parser.instruction_text,
                'error': '无法从HTML文件中提取CAPTCHA图像'
            }

        if not required_chars:
            return {
                'success': False,
                'image_size': captcha_image.size,
                'required_chars': [],
                'instruction_text': parser.instruction_text,
                'error': '无法从HTML文件中提取字符指令'
            }

        return {
            'success': True,
            'image_size': captcha_image.size,
            'required_chars': required_chars,
            'instruction_text': parser.instruction_text
        }

    except Exception as e:
        return {
            'success': False,
            'required_chars': [],
            'instruction_text': '',
            'error': f'解析过程中发生错误: {str(e)}'
        }


def batch_solve_captcha(html_files: List[str], **kwargs) -> Dict[str, Dict]:
    """
    批量处理多个CAPTCHA文件

    Args:
        html_files: HTML文件路径列表
        **kwargs: 传递给solve_captcha的其他参数

    Returns:
        Dict[str, Dict]: 文件名到结果的映射

    Example:
        >>> files = ['captcha1.html', 'captcha2.html']
        >>> results = batch_solve_captcha(files)
        >>> for filename, result in results.items():
        ...     print(f"{filename}: {'成功' if result['success'] else '失败'}")
    """
    results = {}

    for html_file in html_files:
        print(f"处理文件: {html_file}")
        results[html_file] = solve_captcha(html_file, **kwargs)

    return results


def main():
    """主函数 - 命令行模式"""
    print("CAPTCHA验证码自动解决器 - 生产版本")
    print("支持命令行运行和模块导入两种使用方式")
    print("-" * 60)

    # 检查HTML文件
    html_file = '1.html'
    if not os.path.exists(html_file):
        print(f"错误: 找不到HTML文件 '{html_file}'")
        print("请确保HTML文件存在于当前目录")
        return

    try:
        print("🔍 使用公共API进行CAPTCHA解决...")

        # 使用公共API解决CAPTCHA
        result = solve_captcha(
            html_file_path=html_file,
            use_gpu=False,
            similarity_threshold=0.6,
            save_debug_images=True,
            verbose=True
        )

        # 显示详细结果
        print("\n" + "="*60)
        print("📊 详细解决结果")
        print("="*60)

        if result['success']:
            print("✅ 解决成功!")
            print(f"📝 字符序列: {result['required_chars']}")
            print(f"⏱️  处理时间: {result['processing_time']:.2f} 秒")
            print(f"📈 成功率: {result['statistics']['success_rate']:.1%}")

            print("\n📍 坐标结果:")
            coordinates = result['coordinates']
            for i, (char, coord) in enumerate(zip(result['required_chars'], coordinates)):
                if coord:
                    print(f"  {i+1}. '{char}' → 坐标: {coord}")
                else:
                    print(f"  {i+1}. '{char}' → ❌ 未找到")

            print("\n🎯 模块导入使用示例:")
            print("```python")
            print("import captcha_solver")
            print("")
            print("# 方法1: 完整结果")
            print("result = captcha_solver.solve_captcha('1.html')")
            print("if result['success']:")
            print("    coordinates = result['coordinates']")
            print("    print(f'坐标: {coordinates}')")
            print("")
            print("# 方法2: 仅获取坐标")
            print("coordinates = captcha_solver.get_coordinates_only('1.html')")
            print("print(f'坐标列表: {coordinates}')")
            print("")
            print("# 方法3: 提取CAPTCHA信息")
            print("info = captcha_solver.extract_captcha_info('1.html')")
            print("print(f'需要点击的字符: {info[\"required_chars\"]}')")
            print("```")

            # 导出坐标到文件
            solver = OptimizedCaptchaSolver(use_gpu=False, similarity_threshold=0.6)
            solver.export_coordinates_to_file(result)

        else:
            print("❌ 解决失败!")
            if 'error' in result:
                print(f"错误信息: {result['error']}")

        print("\n" + "="*60)
        print("🔧 API函数说明:")
        print("  solve_captcha()        - 完整的CAPTCHA解决方案")
        print("  get_coordinates_only() - 仅返回坐标列表")
        print("  extract_captcha_info() - 仅提取CAPTCHA信息")
        print("  batch_solve_captcha()  - 批量处理多个文件")
        print("="*60)

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


def demo_api_usage():
    """演示API使用方法"""
    print("\n🚀 API使用演示:")
    print("-" * 40)

    html_file = '1.html'
    if not os.path.exists(html_file):
        print(f"演示需要 {html_file} 文件")
        return

    # 演示1: 提取CAPTCHA信息
    print("1. 提取CAPTCHA信息:")
    info = extract_captcha_info(html_file)
    if info['success']:
        print(f"   字符序列: {info['required_chars']}")
        print(f"   图像尺寸: {info.get('image_size', 'N/A')}")
        print(f"   指令文本: '{info['instruction_text']}'")
    else:
        print(f"   失败: {info['error']}")

    # 演示2: 仅获取坐标
    print("\n2. 仅获取坐标:")
    coordinates = get_coordinates_only(html_file, verbose=False)
    print(f"   坐标列表: {coordinates}")

    # 演示3: 完整解决方案
    print("\n3. 完整解决方案:")
    result = solve_captcha(html_file, verbose=False)
    if result['success']:
        print(f"   成功率: {result['statistics']['success_rate']:.1%}")
        print(f"   处理时间: {result['processing_time']:.2f}秒")
    else:
        print(f"   失败: {result['error']}")


if __name__ == "__main__":
    main()

    # 可选：运行API演示
    # demo_api_usage()


if __name__ == "__main__":
    main()

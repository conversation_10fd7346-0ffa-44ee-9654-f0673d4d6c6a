#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动下载和设置ChromeDriver
"""

import os
import sys
import requests
import zipfile
import json
import subprocess
from pathlib import Path

def get_chrome_version():
    """获取Chrome浏览器版本"""
    try:
        # Windows
        if sys.platform == "win32":
            import winreg
            try:
                # 尝试从注册表获取Chrome版本
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                return version
            except:
                # 备用方案：通过命令行获取
                try:
                    result = subprocess.run([
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe", "--version"
                    ], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version
                except:
                    pass
        
        # Linux/Mac
        else:
            try:
                result = subprocess.run(["google-chrome", "--version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    return version
            except:
                pass
        
        return None
    except Exception as e:
        print(f"获取Chrome版本失败: {e}")
        return None

def get_chromedriver_download_url(chrome_version):
    """获取ChromeDriver下载URL"""
    try:
        # 获取主版本号
        major_version = chrome_version.split('.')[0]
        
        # Chrome 115+使用新的API
        if int(major_version) >= 115:
            api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 查找匹配的版本
            for version_info in data['versions']:
                if version_info['version'].startswith(major_version + '.'):
                    downloads = version_info.get('downloads', {})
                    chromedriver = downloads.get('chromedriver', [])
                    
                    for download in chromedriver:
                        if download['platform'] == 'win64':
                            return download['url']
        
        # 旧版本API（Chrome 114及以下）
        else:
            api_url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            
            latest_version = response.text.strip()
            download_url = f"https://chromedriver.storage.googleapis.com/{latest_version}/chromedriver_win32.zip"
            return download_url
        
        return None
        
    except Exception as e:
        print(f"获取ChromeDriver下载URL失败: {e}")
        return None

def download_chromedriver(url, output_path):
    """下载ChromeDriver"""
    try:
        print(f"正在下载ChromeDriver: {url}")
        
        response = requests.get(url, timeout=300)
        response.raise_for_status()
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
        
        print(f"下载完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"下载ChromeDriver失败: {e}")
        return False

def extract_chromedriver(zip_path, extract_to):
    """解压ChromeDriver"""
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        
        # 查找chromedriver.exe文件
        for root, dirs, files in os.walk(extract_to):
            for file in files:
                if file == 'chromedriver.exe':
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join(extract_to, 'chromedriver.exe')
                    
                    if src_path != dst_path:
                        os.rename(src_path, dst_path)
                    
                    print(f"ChromeDriver解压完成: {dst_path}")
                    return dst_path
        
        return None
        
    except Exception as e:
        print(f"解压ChromeDriver失败: {e}")
        return None

def main():
    """主函数"""
    print("🔧 ChromeDriver自动设置工具")
    print("=" * 40)
    
    # 获取Chrome版本
    print("1. 检测Chrome浏览器版本...")
    chrome_version = get_chrome_version()
    
    if not chrome_version:
        print("❌ 无法检测Chrome版本，请确保已安装Chrome浏览器")
        return False
    
    print(f"✅ 检测到Chrome版本: {chrome_version}")
    
    # 获取下载URL
    print("2. 获取ChromeDriver下载链接...")
    download_url = get_chromedriver_download_url(chrome_version)
    
    if not download_url:
        print("❌ 无法获取ChromeDriver下载链接")
        return False
    
    print(f"✅ 找到下载链接: {download_url}")
    
    # 下载ChromeDriver
    print("3. 下载ChromeDriver...")
    zip_path = "chromedriver.zip"
    
    if not download_chromedriver(download_url, zip_path):
        return False
    
    # 解压ChromeDriver
    print("4. 解压ChromeDriver...")
    current_dir = os.getcwd()
    chromedriver_path = extract_chromedriver(zip_path, current_dir)
    
    if not chromedriver_path:
        print("❌ 解压ChromeDriver失败")
        return False
    
    # 清理临时文件
    try:
        os.remove(zip_path)
        print("✅ 清理临时文件完成")
    except:
        pass
    
    # 验证ChromeDriver
    print("5. 验证ChromeDriver...")
    try:
        result = subprocess.run([chromedriver_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ ChromeDriver验证成功: {version}")
        else:
            print("❌ ChromeDriver验证失败")
            return False
    except Exception as e:
        print(f"❌ ChromeDriver验证失败: {e}")
        return False
    
    print("\n🎉 ChromeDriver设置完成!")
    print(f"ChromeDriver路径: {chromedriver_path}")
    print("现在可以运行selenium_auto_login.py了")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ ChromeDriver设置失败")
            print("请手动下载ChromeDriver:")
            print("1. 访问: https://chromedriver.chromium.org/")
            print("2. 下载对应Chrome版本的ChromeDriver")
            print("3. 解压到当前目录或添加到PATH环境变量")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Selenium自动化登录脚本
集成captcha_solver进行验证码识别和点击
"""

import time
import os
import sys
from typing import List, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import captcha_solver


class AutoLoginBot:
    """自动登录机器人"""
    
    def __init__(self, headless: bool = False, timeout: int = 30):
        """
        初始化自动登录机器人
        
        Args:
            headless: 是否使用无头模式
            timeout: 等待超时时间（秒）
        """
        self.timeout = timeout
        self.driver = None
        self.wait = None
        self.actions = None
        self.setup_driver(headless)
        
    def setup_driver(self, headless: bool = False):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            
            # 基本设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 设置用户代理
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            if headless:
                chrome_options.add_argument('--headless')
            
            # 初始化驱动
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置等待和动作链
            self.wait = WebDriverWait(self.driver, self.timeout)
            self.actions = ActionChains(self.driver)
            
            print("✅ Chrome驱动初始化成功")
            
        except Exception as e:
            print(f"❌ Chrome驱动初始化失败: {e}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            raise
    
    def open_website(self, url: str) -> bool:
        """
        打开网站
        
        Args:
            url: 网站URL
            
        Returns:
            bool: 是否成功打开
        """
        try:
            print(f"🌐 正在打开网站: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            print("✅ 网站打开成功")
            return True
            
        except Exception as e:
            print(f"❌ 打开网站失败: {e}")
            return False
    
    def hover_element(self, xpath: str) -> bool:
        """
        鼠标悬停到指定元素
        
        Args:
            xpath: 元素的XPath
            
        Returns:
            bool: 是否成功悬停
        """
        try:
            print(f"🖱️  正在悬停到元素: {xpath}")
            element = self.wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
            self.actions.move_to_element(element).perform()
            time.sleep(1)  # 等待悬停效果
            print("✅ 悬停成功")
            return True
            
        except Exception as e:
            print(f"❌ 悬停失败: {e}")
            return False
    
    def click_element(self, xpath: str, wait_for_load: bool = True) -> bool:
        """
        点击指定元素
        
        Args:
            xpath: 元素的XPath
            wait_for_load: 是否等待页面加载
            
        Returns:
            bool: 是否成功点击
        """
        try:
            print(f"👆 正在点击元素: {xpath}")
            element = self.wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            element.click()
            
            if wait_for_load:
                time.sleep(2)  # 等待页面响应
                
            print("✅ 点击成功")
            return True
            
        except Exception as e:
            print(f"❌ 点击失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = None) -> bool:
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间，默认使用实例设置
            
        Returns:
            bool: 是否加载完成
        """
        try:
            if timeout is None:
                timeout = self.timeout
                
            print(f"⏳ 等待页面加载完成 (最多{timeout}秒)...")
            
            # 等待页面就绪状态
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            time.sleep(2)  # 额外等待时间确保页面完全加载
            print("✅ 页面加载完成")
            return True
            
        except TimeoutException:
            print(f"⚠️  页面加载超时 ({timeout}秒)")
            return False
        except Exception as e:
            print(f"❌ 等待页面加载失败: {e}")
            return False
    
    def input_credentials(self, username: str, password: str, 
                         username_xpath: str = None, password_xpath: str = None) -> bool:
        """
        输入用户名和密码
        
        Args:
            username: 用户名
            password: 密码
            username_xpath: 用户名输入框XPath（可选）
            password_xpath: 密码输入框XPath（可选）
            
        Returns:
            bool: 是否成功输入
        """
        try:
            print("📝 正在输入登录凭据...")
            
            # 如果没有提供XPath，尝试常见的选择器
            if username_xpath is None:
                username_selectors = [
                    "//input[@type='text']",
                    "//input[@name='username']",
                    "//input[@name='user']",
                    "//input[@id='username']",
                    "//input[@placeholder*='用户名']",
                    "//input[@placeholder*='账号']"
                ]
                
                for selector in username_selectors:
                    try:
                        username_field = self.driver.find_element(By.XPATH, selector)
                        break
                    except NoSuchElementException:
                        continue
                else:
                    print("❌ 未找到用户名输入框")
                    return False
            else:
                username_field = self.wait.until(EC.presence_of_element_located((By.XPATH, username_xpath)))
            
            # 输入用户名
            username_field.clear()
            username_field.send_keys(username)
            print(f"✅ 用户名输入完成: {username}")
            
            # 查找密码输入框
            if password_xpath is None:
                password_selectors = [
                    "//input[@type='password']",
                    "//input[@name='password']",
                    "//input[@name='pwd']",
                    "//input[@id='password']",
                    "//input[@placeholder*='密码']"
                ]
                
                for selector in password_selectors:
                    try:
                        password_field = self.driver.find_element(By.XPATH, selector)
                        break
                    except NoSuchElementException:
                        continue
                else:
                    print("❌ 未找到密码输入框")
                    return False
            else:
                password_field = self.wait.until(EC.presence_of_element_located((By.XPATH, password_xpath)))
            
            # 输入密码
            password_field.clear()
            password_field.send_keys(password)
            print("✅ 密码输入完成")
            
            return True

        except Exception as e:
            print(f"❌ 输入凭据失败: {e}")
            return False

    def solve_captcha_and_click(self) -> bool:
        """
        识别并点击验证码

        Returns:
            bool: 是否成功解决验证码
        """
        try:
            print("🔍 正在识别验证码...")

            # 保存当前页面HTML
            html_content = self.driver.page_source
            temp_html_file = "temp_captcha.html"

            with open(temp_html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # 使用captcha_solver识别验证码
            result = captcha_solver.solve_captcha(temp_html_file, verbose=True)

            # 清理临时文件
            if os.path.exists(temp_html_file):
                os.remove(temp_html_file)

            if not result['success']:
                print(f"❌ 验证码识别失败: {result['error']}")
                return False

            coordinates = result['coordinates']
            required_chars = result['required_chars']

            print(f"✅ 验证码识别成功!")
            print(f"📝 需要点击的字符: {required_chars}")
            print(f"📍 点击坐标: {coordinates}")

            # 执行点击操作
            return self.click_captcha_coordinates(coordinates, required_chars)

        except Exception as e:
            print(f"❌ 验证码处理失败: {e}")
            return False

    def click_captcha_coordinates(self, coordinates: List[Tuple[int, int]],
                                 required_chars: List[str]) -> bool:
        """
        按顺序点击验证码坐标

        Args:
            coordinates: 坐标列表
            required_chars: 对应的字符列表

        Returns:
            bool: 是否成功点击所有坐标
        """
        try:
            print("🎯 开始点击验证码坐标...")

            # 查找验证码图片元素（用于定位相对坐标）
            captcha_selectors = [
                "//img[contains(@src, 'captcha')]",
                "//img[contains(@src, 'verify')]",
                "//img[contains(@src, 'code')]",
                "//canvas",
                "//div[contains(@class, 'captcha')]//img",
                "//div[contains(@class, 'verify')]//img"
            ]

            captcha_element = None
            for selector in captcha_selectors:
                try:
                    captcha_element = self.driver.find_element(By.XPATH, selector)
                    break
                except NoSuchElementException:
                    continue

            if captcha_element is None:
                print("⚠️  未找到验证码元素，使用绝对坐标点击")
                # 使用绝对坐标点击
                for i, coord in enumerate(coordinates):
                    if coord:
                        x, y = coord
                        char = required_chars[i] if i < len(required_chars) else f"第{i+1}个"
                        print(f"  第{i+1}步: 点击 '{char}' 坐标 ({x}, {y})")

                        # 使用JavaScript点击（更可靠）
                        self.driver.execute_script(f"document.elementFromPoint({x}, {y}).click();")
                        time.sleep(0.5)
                    else:
                        print(f"  第{i+1}步: 坐标缺失，跳过")
            else:
                print("✅ 找到验证码元素，使用相对坐标点击")
                # 获取验证码元素的位置和大小
                element_location = captcha_element.location
                element_size = captcha_element.size

                print(f"验证码元素位置: {element_location}")
                print(f"验证码元素大小: {element_size}")

                # 使用相对坐标点击
                for i, coord in enumerate(coordinates):
                    if coord:
                        x, y = coord
                        char = required_chars[i] if i < len(required_chars) else f"第{i+1}个"

                        # 计算相对于验证码元素的坐标
                        relative_x = element_location['x'] + x
                        relative_y = element_location['y'] + y

                        print(f"  第{i+1}步: 点击 '{char}' 相对坐标 ({relative_x}, {relative_y})")

                        # 使用ActionChains点击
                        self.actions.move_to_element_with_offset(captcha_element, x, y).click().perform()
                        time.sleep(0.5)
                    else:
                        print(f"  第{i+1}步: 坐标缺失，跳过")

            print("✅ 验证码点击完成")
            time.sleep(2)  # 等待验证码验证
            return True

        except Exception as e:
            print(f"❌ 点击验证码失败: {e}")
            return False

    def click_login_button(self, login_button_xpath: str = None) -> bool:
        """
        点击登录按钮

        Args:
            login_button_xpath: 登录按钮XPath（可选）

        Returns:
            bool: 是否成功点击
        """
        try:
            print("🔐 正在点击登录按钮...")

            if login_button_xpath is None:
                # 尝试常见的登录按钮选择器
                login_selectors = [
                    "//button[contains(text(), '登录')]",
                    "//button[contains(text(), '登陆')]",
                    "//button[contains(text(), 'Login')]",
                    "//input[@type='submit']",
                    "//button[@type='submit']",
                    "//a[contains(text(), '登录')]",
                    "//div[contains(@class, 'login')]//button",
                    "//form//button[last()]"
                ]

                for selector in login_selectors:
                    try:
                        login_button = self.driver.find_element(By.XPATH, selector)
                        break
                    except NoSuchElementException:
                        continue
                else:
                    print("❌ 未找到登录按钮")
                    return False
            else:
                login_button = self.wait.until(EC.element_to_be_clickable((By.XPATH, login_button_xpath)))

            # 点击登录按钮
            login_button.click()
            print("✅ 登录按钮点击成功")

            return True

        except Exception as e:
            print(f"❌ 点击登录按钮失败: {e}")
            return False

    def wait_for_login_result(self, success_indicators: List[str] = None,
                             error_indicators: List[str] = None,
                             timeout: int = 30) -> str:
        """
        等待登录结果

        Args:
            success_indicators: 成功指示器列表（XPath或文本）
            error_indicators: 错误指示器列表（XPath或文本）
            timeout: 超时时间

        Returns:
            str: 'success', 'error', 或 'timeout'
        """
        try:
            print(f"⏳ 等待登录结果 (最多{timeout}秒)...")

            if success_indicators is None:
                success_indicators = [
                    "//div[contains(text(), '登录成功')]",
                    "//div[contains(text(), '欢迎')]",
                    "//a[contains(text(), '退出')]",
                    "//a[contains(text(), '注销')]",
                    "//div[contains(@class, 'user-info')]",
                    "//div[contains(@class, 'dashboard')]"
                ]

            if error_indicators is None:
                error_indicators = [
                    "//div[contains(text(), '登录失败')]",
                    "//div[contains(text(), '用户名或密码错误')]",
                    "//div[contains(text(), '验证码错误')]",
                    "//div[contains(text(), '错误')]",
                    "//div[contains(@class, 'error')]",
                    "//div[contains(@class, 'alert')]"
                ]

            start_time = time.time()

            while time.time() - start_time < timeout:
                # 检查成功指示器
                for indicator in success_indicators:
                    try:
                        element = self.driver.find_element(By.XPATH, indicator)
                        if element.is_displayed():
                            print(f"✅ 登录成功! 检测到成功指示器: {indicator}")
                            return 'success'
                    except NoSuchElementException:
                        pass

                # 检查错误指示器
                for indicator in error_indicators:
                    try:
                        element = self.driver.find_element(By.XPATH, indicator)
                        if element.is_displayed():
                            error_text = element.text
                            print(f"❌ 登录失败! 错误信息: {error_text}")
                            return 'error'
                    except NoSuchElementException:
                        pass

                # 检查URL变化（可能表示登录成功）
                current_url = self.driver.current_url
                if 'login' not in current_url.lower() and 'signin' not in current_url.lower():
                    print(f"✅ 登录成功! URL已变化: {current_url}")
                    return 'success'

                time.sleep(1)

            print(f"⏰ 等待登录结果超时 ({timeout}秒)")
            return 'timeout'

        except Exception as e:
            print(f"❌ 等待登录结果失败: {e}")
            return 'error'

    def auto_login_process(self, url: str, username: str, password: str,
                          hover_xpath: str, click_xpath: str, tab_xpath: str) -> bool:
        """
        完整的自动登录流程

        Args:
            url: 网站URL
            username: 用户名
            password: 密码
            hover_xpath: 需要悬停的元素XPath
            click_xpath: 需要点击的元素XPath
            tab_xpath: 登录选项卡XPath

        Returns:
            bool: 是否登录成功
        """
        try:
            print("🚀 开始自动登录流程...")

            # 步骤1: 打开网站
            if not self.open_website(url):
                return False

            # 步骤2: 鼠标悬停
            if not self.hover_element(hover_xpath):
                return False

            # 步骤3: 点击元素
            if not self.click_element(click_xpath):
                return False

            # 步骤4: 等待页面跳转
            if not self.wait_for_page_load():
                return False

            # 步骤5: 点击登录选项卡
            if not self.click_element(tab_xpath):
                return False

            # 步骤6: 输入账号密码
            if not self.input_credentials(username, password):
                return False

            # 步骤7: 识别并点击验证码
            if not self.solve_captcha_and_click():
                print("⚠️  验证码处理失败，尝试继续登录...")

            # 步骤8: 点击登录按钮
            if not self.click_login_button():
                return False

            # 步骤9: 等待登录结果
            result = self.wait_for_login_result()

            if result == 'success':
                print("🎉 自动登录成功!")
                return True
            elif result == 'error':
                print("❌ 登录失败，请检查账号密码或验证码")
                return False
            else:
                print("⏰ 登录超时，请手动检查")
                return False

        except Exception as e:
            print(f"❌ 自动登录流程失败: {e}")
            return False

    def close(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                print("✅ 浏览器已关闭")
        except Exception as e:
            print(f"⚠️  关闭浏览器时出错: {e}")


def main():
    """主函数 - 使用示例"""
    # 配置参数
    config = {
        'url': 'https://ysfw.mot.gov.cn/NetRoadCGSS-web/',
        'hover_xpath': '/html/body/div/div[6]/div/div[2]/div[1]/div[1]',
        'click_xpath': '/html/body/div/div[6]/div/div[2]/div[1]/div[1]/li/a[1]/p[1]',
        'tab_xpath': '/html/body/div[2]/div/div/div[1]/ul/li[2]',
        'username': '',  # 请填入您的用户名
        'password': '',  # 请填入您的密码
        'headless': False,  # 是否使用无头模式
        'timeout': 30  # 超时时间（秒）
    }

    # 检查必要参数
    if not config['username'] or not config['password']:
        print("❌ 请在脚本中填入用户名和密码")
        print("请编辑 main() 函数中的 config 字典")
        return

    # 创建自动登录机器人
    bot = None
    try:
        print("🤖 初始化自动登录机器人...")
        bot = AutoLoginBot(headless=config['headless'], timeout=config['timeout'])

        # 执行自动登录
        success = bot.auto_login_process(
            url=config['url'],
            username=config['username'],
            password=config['password'],
            hover_xpath=config['hover_xpath'],
            click_xpath=config['click_xpath'],
            tab_xpath=config['tab_xpath']
        )

        if success:
            print("🎉 自动登录完成!")
            print("浏览器将保持打开状态，您可以继续操作...")

            # 保持浏览器打开，等待用户操作
            input("按回车键关闭浏览器...")
        else:
            print("❌ 自动登录失败")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if bot:
            bot.close()


def test_captcha_only():
    """仅测试验证码识别功能"""
    print("🧪 测试验证码识别功能...")

    bot = None
    try:
        bot = AutoLoginBot(headless=False, timeout=30)

        # 打开网站并导航到验证码页面
        bot.open_website('https://ysfw.mot.gov.cn/NetRoadCGSS-web/')
        bot.hover_element('/html/body/div/div[6]/div/div[2]/div[1]/div[1]')
        bot.click_element('/html/body/div/div[6]/div/div[2]/div[1]/div[1]/li/a[1]/p[1]')
        bot.wait_for_page_load()
        bot.click_element('/html/body/div[2]/div/div/div[1]/ul/li[2]')

        print("请在浏览器中手动输入用户名和密码，然后按回车继续测试验证码...")
        input("按回车键开始测试验证码识别...")

        # 测试验证码识别
        if bot.solve_captcha_and_click():
            print("✅ 验证码识别测试成功!")
        else:
            print("❌ 验证码识别测试失败")

        input("按回车键关闭浏览器...")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if bot:
            bot.close()


if __name__ == "__main__":
    print("🎯 Selenium自动登录脚本")
    print("=" * 50)
    print("选择运行模式:")
    print("1. 完整自动登录流程")
    print("2. 仅测试验证码识别")
    print("3. 退出")

    choice = input("请输入选择 (1-3): ").strip()

    if choice == '1':
        main()
    elif choice == '2':
        test_captcha_only()
    elif choice == '3':
        print("👋 再见!")
    else:
        print("❌ 无效选择")
